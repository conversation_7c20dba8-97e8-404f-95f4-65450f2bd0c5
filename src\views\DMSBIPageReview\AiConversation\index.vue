<template>
  <div class="ai-conversation" :data-theme="darkTheme ? 'dark' : 'light'">
    <!-- 复制提示 -->
    <div class="copy-toast" v-if="showCopyToast" :class="{ success: copySuccess }">
      <span>{{ copySuccess ? "复制成功" : "复制失败" }}</span>
    </div>

    <!-- 消息容器 -->
    <div class="message-container">
      <!-- 消息列表 -->
      <div v-for="(message, index) in aiConversationMessages" :key="index"
        :class="[message.type === 'ai' ? 'ai-message' : 'user-message']">
        <div class="avatar" v-if="message.type === 'ai'">
          <img :src="require('@/assets/images/workShop_standing_meeting/ai_avatar.png')
            " alt="AI Avatar" />
        </div>
        <div class="message-content">
          <div class="message-body">
            <template v-if="message.isMarkdown">
              <div class="markdown-content" v-html="renderMarkdown(message.content)"></div>
            </template>
            <template v-else>
              {{ message.content }}
            </template>
          </div>
          <div class="message-footer" :class="{
            'ai-footer': message.type === 'ai',
            'user-footer': message.type === 'user',
          }">
            <div class="speech-controls" v-if="message.type === 'ai'">
              <img v-if="!isMessagePlaying(message)" src="@/assets/images/workShop_standing_meeting/readText.png"
                alt="播放文本" class="footer-icon" @click="handlePlayText(message)" />
              <template v-else>
                <img v-if="isSpeechPaused" src="@/assets/images/workShop_standing_meeting/play.png" alt="继续播放"
                  class="footer-icon" @click="handleResumeSpeech" />
                <img v-else src="@/assets/images/workShop_standing_meeting/pause.png" alt="暂停播放" class="footer-icon"
                  @click="handlePauseSpeech" />
                <img src="@/assets/images/workShop_standing_meeting/stop.png" alt="停止播放" class="footer-icon"
                  @click="handleStopSpeech" />
              </template>
            </div>
            <img src="@/assets/images/workShop_standing_meeting/copy_icon.png" alt="复制文本" class="footer-icon2"
              @click="handleToMsgTool(message, 'copyText')" />
            <img v-if="message.type === 'ai'" src="@/assets/images/workShop_standing_meeting/good_answer.png"
              alt="较好的回答" class="footer-icon2" @click="handleToMsgTool(message, 'goodAnswer')" />
            <img v-if="message.type === 'ai'" src="@/assets/images/workShop_standing_meeting/bed_answer.png" alt="较差的回答"
              class="footer-icon2" @click="handleToMsgTool(message, 'badAnswer')" />
            <a-button v-if="message.type === 'ai'" class="adopt-btn"
              @click="handlePushToSummarize(message)">采纳</a-button>
          </div>
        </div>
        <div class="avatar" v-if="message.type === 'user'">
          <img :src="userAvatar" alt="User Avatar" />
        </div>
      </div>
    </div>

    <!-- 底部操作区 -->
    <div class="conversation-footer">
      <div class="input-area">
        <a-textarea placeholder="发消息..." v-model="inputMessage" :auto-size="false" @pressEnter="handleEnterPress">
        </a-textarea>
        <div class="send-button" @click="sendMessage">
          <img :src="require('@/assets/images/workShop_standing_meeting/ai_conversation_sendBtnIcon.png')
            " alt="发送" />
        </div>
      </div>
      <div class="button-group">
        <a-button class="summary-btn" @click="handleSummaryClick('daily')">全天总结</a-button>
        <a-button class="summary-btn" @click="handleSummaryClick('periodOfTime')">时段总结</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  speak,
  handleAiCopy,
  pauseSpeech,
  resumeSpeech,
  stopSpeech,
  renderMarkdown,
  requestSSE
} from "./utils.js";
import moment from "moment";

export default {
  name: "AiConversation",
  props: {
    darkTheme: {
      type: Boolean,
      default: false,
    },
    userAvatar: {
      type: String,
      default: require("@/assets/images/workShop_standing_meeting/user_avatar.png"),
    },
    searchForm: {
      type: Object,
    },
    date: {
      type: String,
      default: moment().format('YYYY-MM-DD'),
    },
    isInitializingOrg: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // 监听组件显示状态变化
    visible: {
      handler(newVal) {
        // 当组件显示时，检查是否需要发送初始请求
        if (newVal) {
          this.handleComponentVisible();
        }
      },
      immediate: true
    },
    // 监听组织层级变化
    searchForm: {
      handler(newVal, oldVal) {
        // 当组织层级发生变化时，清空对话记录和重置状态
        if (oldVal && this.hasOrgLevelChanged(newVal, oldVal)) {
          console.log('组织层级发生变化，清空对话记录');
          this.resetConversation();
        }
      },
      deep: true
    }
  },
  beforeDestroy() {
    // 组件销毁前取消任何正在进行的SSE请求
    if (this.currentSSEController) {
      this.currentSSEController.abort();
      this.currentSSEController = null;
    }
    // 停止任何正在播放的语音
    stopSpeech();
  },
  data() {
    return {
      inputMessage: "",
      activeButton: "",
      currentPlayingMessage: null,
      isSpeechPaused: false,
      showCopyToast: false,
      copySuccess: false,
      currentSSEController: null, // 用于控制SSE请求
      hasInitialRequestSent: false, // 标记是否已发送初始请求
      aiConversationMessages: [
      ],
    };
  },
  methods: {
    // 处理组件显示时的逻辑
    handleComponentVisible() {
      // 只有在组件显示、组织层级初始化完成且还没有发送过初始请求时才发送
      if (!this.isInitializingOrg && !this.hasInitialRequestSent) {
        console.log('AI组件显示且组织层级初始化完成，自动发送时段总结请求');
        this.hasInitialRequestSent = true;
        this.handleSummaryClick('periodOfTime');
      } else {
        console.log('AI组件显示，但条件不满足', {
          isInitializingOrg: this.isInitializingOrg,
          hasInitialRequestSent: this.hasInitialRequestSent
        });
      }
    },
    // 检查组织层级是否发生变化
    hasOrgLevelChanged(newForm, oldForm) {
      const orgKeys = ['baseCode', 'factoryCode', 'branchCode', 'workshopCode', 'lineCode', 'teamCode'];
      return orgKeys.some(key => newForm[key] !== oldForm[key]);
    },
    // 重置对话状态
    resetConversation() {
      // 清空对话记录
      this.aiConversationMessages = [];
      // 重置初始请求标志
      this.hasInitialRequestSent = false;
      // 取消当前正在进行的请求
      if (this.currentSSEController) {
        this.currentSSEController.abort();
        this.currentSSEController = null;
      }
      // 停止语音播放
      this.currentPlayingMessage = null;
      this.isSpeechPaused = false;
    },
    // 渲染Markdown内容
    renderMarkdown(content) {
      return renderMarkdown(content);
    },
    // 发送消息
    sendMessage() {
      if (!this.inputMessage.trim()) return;

      const userMessage = this.inputMessage;

      // 添加用户消息
      this.aiConversationMessages.push({
        type: "user",
        content: userMessage,
        isMarkdown: false,
      });

      // 清空输入框
      this.inputMessage = "";

      // 添加一个临时的AI消息，表示正在思考
      const tempMessageIndex = this.aiConversationMessages.length;
      this.aiConversationMessages.push({
        type: "ai",
        content: "思考中...",
        isMarkdown: false,
        isLoading: true,
      });

      // 使用SSE工具函数请求AI回复
      this.currentSSEController = requestSSE({
        url: '/api/mom-ai/ai/agent/run?clientKey=2ca95df979034c669d34b7ea8f37db72&appId=79f6161a58ca00a981559f5c9918d2da',
        method: 'POST',
        body: {
          // this.$store.state.user.info.loginName ||
          "user_id": this.$store.state.user.info.loginName || "yueshengqi.ex",
          // "conversation_id": ctx.state.conversation_id,
          "inputs": {
            //  this.$store.state.user.info.loginName ||
            userId: this.$store?.state.user.info.loginName || "yueshengqi.ex",
            companyCode: this.searchForm.baseCode,
            companyName: this.searchForm.baseName,
            plantName: this.searchForm.factoryName,
            plantCode: this.searchForm.factoryCode,
            subFactoryName: this.searchForm.branchName,
            subFactoryCode: this.searchForm.branchCode,
            workshopCode: this.searchForm.workshopCode,
            workshopName: this.searchForm.workshopName,
            teamCode: this.searchForm.teamCode,
            teamName: this.searchForm.teamName,
            lineName: this.searchForm.lineName,
            lineCode: this.searchForm.lineCode,
            targetTime: this.date,
            token: sessionStorage.getItem("Access-Token") || "53fe6c4f-5d00-491b-a48a-6366eeebb04a"
          },
          "query": userMessage,
          "response_mode": "streaming",
          "files": []
        },
        onOpen: () => {
          // SSE连接已打开
        },
        onMessage: (data) => {
          // 替换临时消息
          if (this.aiConversationMessages[tempMessageIndex].isLoading) {
            this.aiConversationMessages.splice(tempMessageIndex, 1, {
              type: "ai",
              content: typeof data === 'string' ? data : (data.content || ''),
              isMarkdown: true,
            });
          } else {
            // 如果是流式响应，可以累加内容
            const currentMessage = this.aiConversationMessages[tempMessageIndex];
            this.aiConversationMessages.splice(tempMessageIndex, 1, {
              ...currentMessage,
              content: currentMessage.content + (typeof data === 'string' ? data : (data.answer || '')),
            });
          }
        },
        onError: (error) => {
          // 替换临时消息为错误消息
          this.aiConversationMessages.splice(tempMessageIndex, 1, {
            type: "ai",
            content: "抱歉，请求出错了，请稍后再试。",
            isMarkdown: false,
          });
        },
        onComplete: () => {
          this.currentSSEController = null;
        }
      });
    },
    handleSummaryClick(type) {
      this.activeButton = type;

      // 根据类型发送不同的预设消息
      if (type === 'daily') {
        // 全天总结逻辑
        this.inputMessage = this.generateDailySummaryMessage();
        this.sendMessage();
      } else if (type === "periodOfTime") {
        // 时段总结逻辑
        this.inputMessage = this.generatePeriodSummaryMessage();
        this.sendMessage();
      }
    },

    /**
     * 生成全天总结消息
     */
    generateDailySummaryMessage() {
      const now = moment();
      const currentHour = now.hour();

      if (currentHour >= 8 && currentHour < 20) {
        // 8:00-20:00间：统计当日8点至当前时间
        const startTime = now.clone().hour(8).minute(0).second(0);
        const endTime = now;
        return `请帮我生成${startTime.format('YYYY年MM月DD日HH:mm')}至${endTime.format('HH:mm')}的全天总结`;
      } else if (currentHour >= 20) {
        // 20:00-24:00间：统计当日20点至当前时间
        const startTime = now.clone().hour(20).minute(0).second(0);
        const endTime = now;
        return `请帮我生成${startTime.format('YYYY年MM月DD日HH:mm')}至${endTime.format('HH:mm')}的全天总结`;
      } else {
        // 0:00-8:00间：统计前日20点至当前时间(跨天)
        const yesterday = now.clone().subtract(1, 'day');
        const startTime = yesterday.hour(20).minute(0).second(0);
        const endTime = now;
        return `请帮我生成${startTime.format('YYYY年MM月DD日HH:mm')}至${endTime.format('MM月DD日HH:mm')}的全天总结`;
      }
    },

    /**
     * 生成时段总结消息
     * 根据说明2：直接统计开会时间所属小时的整点和上一次会议结束整点之间的数据
     */
    generatePeriodSummaryMessage() {
      const now = moment();
      const currentHour = now.hour();
      const currentMinute = now.minute();

      // 白班时段(8:00-20:00)
      if (currentHour >= 8 && currentHour < 20) {
        return this.getWhiteShiftPeriod(currentHour, currentMinute, now);
      }
      // 夜班时段(20:00-8:00)
      else {
        return this.getNightShiftPeriod(currentHour, currentMinute, now);
      }
    },

    /**
     * 获取白班时段总结
     */
    getWhiteShiftPeriod(currentHour, currentMinute, now) {
      // 白班会议时间点：9:00, 11:00, 16:00, 18:00
      // 对应统计时段：8-9, 9-11, 11-16, 16-18

      if (currentHour >= 8 && currentHour < 9) {
        // 8:00-8:59：统计8点到当前时间
        const startTime = now.clone().hour(8).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 9) {
        // 9:00-9:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好9:00：统计8-9点
          const startTime = now.clone().hour(8).minute(0).second(0);
          const endTime = now.clone().hour(9).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 9:01-9:59：统计8点到当前时间
          const startTime = now.clone().hour(8).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else if (currentHour >= 10 && currentHour < 11) {
        // 10:00-10:59：统计9点到当前时间
        const startTime = now.clone().hour(9).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 11) {
        // 11:00-11:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好11:00：统计9-11点
          const startTime = now.clone().hour(9).minute(0).second(0);
          const endTime = now.clone().hour(11).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 11:01-11:59：统计9点到当前时间
          const startTime = now.clone().hour(9).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else if (currentHour >= 12 && currentHour < 16) {
        // 12:00-15:59：统计11点到当前时间
        const startTime = now.clone().hour(11).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 16) {
        // 16:00-16:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好16:00：统计11-16点
          const startTime = now.clone().hour(11).minute(0).second(0);
          const endTime = now.clone().hour(16).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 16:01-16:59：统计11点到当前时间
          const startTime = now.clone().hour(11).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else if (currentHour >= 17 && currentHour < 18) {
        // 17:00-17:59：统计16点到当前时间
        const startTime = now.clone().hour(16).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 18) {
        // 18:00-18:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好18:00：统计16-18点
          const startTime = now.clone().hour(16).minute(0).second(0);
          const endTime = now.clone().hour(18).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 18:01-18:59：统计16点到当前时间
          const startTime = now.clone().hour(16).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else {
        // 19:00-19:59：统计18点到当前时间
        const startTime = now.clone().hour(18).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      }
    },

    /**
     * 获取夜班时段总结
     */
    getNightShiftPeriod(currentHour, currentMinute, now) {
      // 夜班会议时间点：21:00, 23:00, 04:00, 06:00
      // 对应统计时段：20-21, 21-23, 23-04, 04-06

      if (currentHour >= 20 && currentHour < 21) {
        // 20:00-20:59：统计20点到当前时间
        const startTime = now.clone().hour(20).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 21) {
        // 21:00-21:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好21:00：统计20-21点
          const startTime = now.clone().hour(20).minute(0).second(0);
          const endTime = now.clone().hour(21).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 21:01-21:59：统计20点到当前时间
          const startTime = now.clone().hour(20).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else if (currentHour === 22) {
        // 22:00-22:59：统计21点到当前时间
        const startTime = now.clone().hour(21).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 23) {
        // 23:00-23:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好23:00：统计21-23点
          const startTime = now.clone().hour(21).minute(0).second(0);
          const endTime = now.clone().hour(23).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 23:01-23:59：统计21点到当前时间
          const startTime = now.clone().hour(21).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else if (currentHour >= 0 && currentHour < 4) {
        // 0:00-3:59：统计前日23点到当前时间(跨天)
        const yesterday = now.clone().subtract(1, 'day');
        const startTime = yesterday.hour(23).minute(0).second(0);
        return `请帮我生成${startTime.format('MM月DD日HH:mm')}至${now.format('MM月DD日HH:mm')}的时段总结`;
      } else if (currentHour === 4) {
        // 4:00-4:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好4:00：统计前日23-今日04点(跨天)
          const yesterday = now.clone().subtract(1, 'day');
          const startTime = yesterday.hour(23).minute(0).second(0);
          const endTime = now.clone().hour(4).minute(0).second(0);
          return `请帮我生成${startTime.format('MM月DD日HH:mm')}至${endTime.format('MM月DD日HH:mm')}的时段总结`;
        } else {
          // 4:01-4:59：统计前日23点到当前时间(跨天)
          const yesterday = now.clone().subtract(1, 'day');
          const startTime = yesterday.hour(23).minute(0).second(0);
          return `请帮我生成${startTime.format('MM月DD日HH:mm')}至${now.format('MM月DD日HH:mm')}的时段总结`;
        }
      } else if (currentHour === 5) {
        // 5:00-5:59：统计4点到当前时间
        const startTime = now.clone().hour(4).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      } else if (currentHour === 6) {
        // 6:00-6:59：根据具体时间判断
        if (currentMinute === 0) {
          // 正好6:00：统计4-6点
          const startTime = now.clone().hour(4).minute(0).second(0);
          const endTime = now.clone().hour(6).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${endTime.format('HH:mm')}的时段总结`;
        } else {
          // 6:01-6:59：统计4点到当前时间
          const startTime = now.clone().hour(4).minute(0).second(0);
          return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
        }
      } else {
        // 7:00-7:59：统计6点到当前时间
        const startTime = now.clone().hour(6).minute(0).second(0);
        return `请帮我生成${startTime.format('HH:mm')}至${now.format('HH:mm')}的时段总结`;
      }
    },
    // 将Markdown文本转换为纯文本
    markdownToPlainText(markdown) {
      // 先使用renderMarkdown将markdown转为HTML
      const html = renderMarkdown(markdown);

      // 创建一个临时的div元素来解析HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;

      // 获取纯文本内容
      const plainText = tempDiv.textContent || tempDiv.innerText || '';

      // 清理文本，去除多余空格和换行
      return plainText
        .replace(/\s+/g, ' ')
        .replace(/\n+/g, ' ')
        .trim();
    },

    // 播放文本
    handlePlayText(message) {
      this.currentPlayingMessage = message;
      this.isSpeechPaused = false;

      // 根据消息类型选择合适的文本处理方式
      let textToSpeak;
      if (message.isMarkdown) {
        // 如果是Markdown格式，先转换为纯文本
        textToSpeak = this.markdownToPlainText(message.content);
      } else {
        // 普通文本直接使用
        textToSpeak = message.content;
      }

      speak({ text: textToSpeak }, () => {
        this.currentPlayingMessage = null;
        this.isSpeechPaused = false;
      });
    },
    // 暂停播放
    handlePauseSpeech() {
      pauseSpeech();
      this.isSpeechPaused = true;
    },
    // 继续播放
    handleResumeSpeech() {
      resumeSpeech();
      this.isSpeechPaused = false;
    },
    // 停止播放
    handleStopSpeech() {
      stopSpeech();
      this.currentPlayingMessage = null;
      this.isSpeechPaused = false;
    },
    // 判断当前消息是否正在播放
    isMessagePlaying(message) {
      return this.currentPlayingMessage === message;
    },
    // 将ai对话当作板块展示
    handlePushToSummarize(message) {
      this.$emit("pushToSummarize", message);
    },
    // 对话框底部工具
    async handleToMsgTool(message, type) {
      switch (type) {
        case "copyText": {
          const success = await handleAiCopy(message.content);
          this.showCopyToast = true;
          this.copySuccess = success;
          setTimeout(() => {
            this.showCopyToast = false;
          }, 2000);
          return;
        }
        case "goodAnswer":
          return;
        case "badAnswer":
          return;
      }
    },
    // 处理Enter键按下事件
    handleEnterPress(e) {
      // 如果按下的是Enter键且没有同时按下Shift键，则发送消息
      if (e.keyCode === 13 && !e.shiftKey) {
        e.preventDefault(); // 阻止默认行为（换行）
        this.sendMessage(); // 调用发送消息方法
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ai-conversation {
  width: 400px;
  background-color: var(--bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: calc(100% - 56px);
  min-height: 0;
  overflow: hidden;

  .message-container {
    flex: 1;
    padding-top: 16px;
    overflow-y: auto;
    min-height: 0;
    width: 100%;
    box-sizing: border-box;
  }

  .ai-message,
  .user-message {
    display: flex;
    margin-bottom: 24px;
    align-items: flex-start;
    width: 100%;
    box-sizing: border-box;
  }

  .ai-message {
    justify-content: flex-start;
  }

  .user-message {
    justify-content: flex-end;
  }

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin: 0 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    max-width: 70%;
    min-width: 100px;
    word-break: break-all;
    overflow-wrap: break-word;

    .message-body {
      font-size: 14px;
      line-height: 1.6;
      color: var(--text-color);
      background: var(--message-bg);
      padding: 12px;
      border-radius: 6px;
      word-wrap: break-word;
      display: inline-block;
      border: var(--message-border);

      ::v-deep .markdown-content {
        p {
          margin: 0 0 10px;
          white-space: pre-line;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 0;
          margin-bottom: 10px;
          font-weight: 600;
          color: var(--text-color);
        }

        ul,
        ol {
          padding-left: 20px;
          margin-bottom: 10px;
        }

        pre {
          background-color: var(--code-bg);
          padding: 10px;
          border-radius: 4px;
          overflow-x: auto;
          color: var(--text-color);
        }

        code {
          background-color: var(--code-bg);
          padding: 2px 4px;
          border-radius: 3px;
          font-family: monospace;
          color: var(--text-color);
        }

        table {
          border-collapse: collapse;
          width: 100%;
          margin-bottom: 10px;

          th,
          td {
            border: 1px solid var(--text-color);
            padding: 8px;
            text-align: left;
          }

          th {
            background-color: var(--table-header-bg);
            color: var(--text-color);
          }
        }

        blockquote {
          border-left: 4px solid var(--text-color);
          padding-left: 10px;
          margin-left: 0;
          margin-right: 0;
          opacity: 0.8;
        }

        img {
          max-width: 100%;
          height: auto;
        }

        a {
          color: #00aaa6;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }

    .message-footer {
      margin-top: 4px;
      display: flex;
      align-items: center;

      &.ai-footer {
        justify-content: flex-start;
      }

      &.user-footer {
        justify-content: flex-end;
      }

      .footer-icon {
        width: 16px;
        height: 16px;
        opacity: 0.6;
        margin: 0 6px;
        cursor: pointer;
      }

      .footer-icon2 {
        width: 12px;
        height: 13px;
        opacity: 0.6;
        margin: 0 6px;
        cursor: pointer;
      }

      .adopt-btn {
        height: 28px;
        padding: 4px 12px;
        border-radius: 107px;
        background: #00aaa6;
        font-size: 14px;
        color: #ffffff;
        margin-left: auto;
      }
    }
  }

  .user-message .message-body {
    background: var(--user-message-bg);
    border: var(--message-border);
  }

  .conversation-footer {
    padding: 16px;
    flex-shrink: 0;

    .input-area {
      margin-bottom: 12px;
      position: relative;

      /deep/ .ant-input {
        width: 368px;
        height: 89px !important;
        border-radius: 16px;
        background: var(--input-bg);
        padding: 16px;
        padding-right: 48px;
        border: none;
        font-size: 14px;
        outline: none;
        resize: none;
        color: var(--text-color);

        &:focus {
          border-color: #00aaa6;
          box-shadow: none;
        }

        &::placeholder {
          color: var(--placeholder-color);
        }
      }

      .send-button {
        position: absolute;
        right: 16px;
        bottom: 16px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #00aaa6;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background: #008784;
        }

        img {
          width: 20px;
          height: 20px;
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;

      .summary-btn {
        //width: 88px;
        height: 32px;
        padding: 5px 16px;
        border-radius: 374px;
        background: var(--button-bg);
        color: var(--text-color);
        border: none;
        font-size: 14px;

        &:hover {
          background: var(--button-hover-bg);
          color: #00aaa6;
        }
      }
    }
  }
}

.ai-conversation {
  --bg-color: #fff;
  --text-color: #1d2129;
  --message-bg: #f2f3f5;
  --user-message-bg: #e8f3ff;
  --message-border: none;
  --input-bg: #f2f3f5;
  --placeholder-color: #86909c;
  --button-bg: #f2f3f5;
  --button-hover-bg: rgba(0, 170, 166, 0.15);
  --code-bg: rgba(0, 0, 0, 0.05);
  --table-header-bg: rgba(0, 0, 0, 0.05);

  &[data-theme="dark"] {
    --bg-color: #1d2129;
    --text-color: #fff;
    --message-bg: #313540;
    --user-message-bg: #164b7e;
    --message-border: 1px solid rgba(255, 255, 255, 0.15);
    --input-bg: #313540;
    --placeholder-color: #86909c;
    --button-bg: #313540;
    --button-hover-bg: rgba(0, 170, 166, 0.25);
    --code-bg: rgba(255, 255, 255, 0.1);
    --table-header-bg: rgba(255, 255, 255, 0.1);
  }
}

.speech-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.copy-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 10px 20px;
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  z-index: 1000;
  animation: fadeInOut 3s ease-in-out;

  &.success {
    background: rgba(0, 170, 166, 0.9);
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }

  10% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  90% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }

  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

.structured-content {

  .production-section,
  .quality-section,
  .abnormal-section {
    margin-bottom: 12px;

    h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      color: var(--text-color);
    }

    p {
      margin: 0;
      white-space: pre-line;
    }
  }
}
</style>
