<!--
 * @Description: 卡片详情
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2021-07-12 10:45:41
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-06-03 15:42:27
-->
<template>
  <a-drawer
    placement="right"
    width="740px"
    :visible="visible"
    @close="close"
    class="CardDetailDrawerWrap2"
    :class="[skinStyle()]"
  >
    <!-- 线上运行 -->
    <!-- <component
      ref="cardDetailInfo"
      :is="compName"
      :dataItem="dataItem"
      :companyName="companyName"
      :signOrgId="signOrgId"
      :pageClass="pageClass"
      @close="close"
      @subscribe="$emit('subscribe')"
    ></component> -->
    <!-- 本地调试 -->
    <CardDetailInfo
      ref="cardDetailInfo"
      :dataItem="dataItem"
      :companyName="companyName"
      :signOrgId="signOrgId"
      :pageClass="pageClass"
      @close="close"
      @subscribe="$emit('subscribe')"
    />
  </a-drawer>
</template>
<script>
// import { publicPath } from "@/utils/utils.js";
import CardDetailInfo from "./cardDetailInfo.vue"; // 本地调试
export default {
  inject: ["skinStyle"],
  components: { CardDetailInfo }, // 本地调试
  props: {
    companyName: String,
    signOrgId: String,
    pageClass: String,
  },
  data() {
    return {
      indexCardDetailInfoJSUrl:
        (window.location.host.includes("localhost")
          ? "http://smc.devapps.hisense.com"
          : "") + "/minio/mombucket/IndexCardDetailInfo2.umd.min.1.0.js",
      visible: false,
      dataItem: {},
      compName: "",
    };
  },
  // mounted() {
  //   if (!window["IndexCardDetailInfo2"]) {
  //     const script = document.createElement("script");
  //     let fileUrl = this.indexCardDetailInfoJSUrl;
  //     if (this.indexCardDetailInfoJSUrl.indexOf("http") === -1) {
  //       fileUrl = `${publicPath}${this.indexCardDetailInfoJSUrl}`;
  //     }
  //     script.src = fileUrl + `?t=${Date.now()}`;
  //     script.onload = () => {
  //       const exportCom = window["IndexCardDetailInfo2"].default;
  //       this.compName = exportCom.myCom;
  //     };
  //     document.body.appendChild(script);
  //   } else {
  //     const exportCom = window["IndexCardDetailInfo2"].default;
  //     this.compName = exportCom.myCom;
  //   }
  // },
  methods: {
    /**
     * @description: 打开抽屉
     * @param {Object} item 指标详情
     */
    show(item) {
      this.dataItem = item;
      this.visible = true;
    },
    // 关闭抽屉
    close() {
      this.dataItem = {};
      this.visible = false;
      this.$refs["cardDetailInfo"].close(false);
    },
  },
};
</script>
<style lang="less">
.CardDetailDrawerWrap2 {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  &.hisense-style.dark {
    .ant-drawer-close {
      color: #c9cdd4;
    }
  }
  .ant-drawer-content {
    height: 100vh !important;
    overflow: hidden !important;
    .ant-drawer-body {
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 0 !important;
      .__center {
        flex: 1;
        overflow-y: auto;
        padding: 0 24px;
        margin-bottom: 10px;
      }
    }
  }
  .ant-drawer-body {
    height: 100%;
    overflow-y: auto;
    position: relative;
  }
}
</style>
