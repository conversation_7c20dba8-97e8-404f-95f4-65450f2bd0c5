# QRCodeModal 二维码模态框组件

## 概述

`QRCodeModal` 是一个独立的二维码模态框组件，用于显示会议签到二维码。该组件封装了二维码的获取、显示、刷新等功能，支持暗色主题，并提供了完整的错误处理和加载状态。

## 功能特性

- ✅ 自动获取二维码
- ✅ 支持二维码刷新
- ✅ 完整的错误处理
- ✅ 加载状态显示
- ✅ 暗色主题支持
- ✅ 响应式设计
- ✅ 时间格式化显示

## 使用方法

### 基本用法

```vue
<template>
  <div>
    <!-- 触发按钮 -->
    <a-button @click="QRCodeVisible = true">
      会议签到
    </a-button>
    
    <!-- 二维码模态框 -->
    <QRCodeModal 
      :visible.sync="QRCodeVisible" 
      :darkTheme="darkTheme" 
      :searchForm="searchForm" 
    />
  </div>
</template>

<script>
import QRCodeModal from './components/QRCodeModal.vue';

export default {
  components: {
    QRCodeModal
  },
  data() {
    return {
      QRCodeVisible: false,
      darkTheme: false,
      searchForm: {
        lineName: '生产线A',
        lineCode: 'LINE_001',
        workshopCode: 'WORKSHOP_001'
      }
    };
  }
};
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 必填 | 说明 |
|--------|------|--------|------|------|
| visible | Boolean | false | 是 | 控制模态框显示/隐藏，支持 .sync 修饰符 |
| darkTheme | Boolean | false | 否 | 是否启用暗色主题 |
| searchForm | Object | {} | 是 | 搜索表单数据，包含线体信息 |

### searchForm 对象结构

```javascript
{
  lineName: '',      // 线体名称，用于显示
  lineCode: '',      // 线体代码，用于验证
  workshopCode: ''   // 车间代码，用于API调用
}
```

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:visible | Boolean | 模态框显示状态变化时触发 |

## 组件状态

### 加载状态
- 显示加载动画和"正在获取二维码..."提示

### 成功状态
- 显示二维码图片
- 显示线体信息
- 显示创建时间和过期时间
- 支持点击图片刷新二维码

### 错误状态
- 显示错误图标和错误信息
- 提供"重新获取"按钮

### 空状态
- 当未选择线体时显示
- 提供"获取二维码"按钮（禁用状态）

## 样式定制

组件使用 scoped 样式，支持暗色主题。主要样式类：

- `.qrcode-content` - 内容容器
- `.qrcode-loading` - 加载状态
- `.qrcode-error` - 错误状态
- `.qrcode-success` - 成功状态
- `.qrcode-empty` - 空状态
- `.qrcode-image-container` - 图片容器
- `.qrcode-info` - 信息显示区域

### 暗色主题样式

组件会根据 `darkTheme` prop 自动应用暗色主题样式：

```css
.dark-theme .qrcode-info {
  background: #001d2c;
  border: 1px solid #00aaa6;
}

.dark-theme .qrcode-info p {
  color: #fff;
}
```

## API 依赖

组件依赖 `getQrCode` API 接口：

```javascript
// 从 ../Api 导入
import { getQrCode } from "../Api";

// API 调用
const result = await getQrCode(workshopCode);
```

## 注意事项

1. **线体选择**: 必须先选择线体（lineCode 不为空）才能获取二维码
2. **自动获取**: 模态框打开时会自动尝试获取二维码
3. **错误处理**: 网络错误和业务错误都有相应的提示
4. **时间格式**: 使用 moment.js 格式化时间为 'YYYY-MM-DD HH:mm:ss'
5. **响应式**: 组件在不同屏幕尺寸下都能正常显示

## 更新日志

### v1.0.0 (2025-01-27)
- 初始版本发布
- 支持基本的二维码获取和显示功能
- 支持暗色主题
- 完整的错误处理和状态管理
