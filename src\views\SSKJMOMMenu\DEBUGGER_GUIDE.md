# 位置调试工具使用指南

## 概述

位置调试工具是一个独立的Vue组件，专门用于手动微调SSKJ MOM菜单中各个节点的位置。通过拖拽操作，您可以精确调整节点位置，并将最终的坐标配置输出到控制台，方便复制到主组件中。

## 访问方式

### 方法一：直接访问
```
http://localhost:8083/position-debugger
```

### 方法二：从首页导航
1. 访问 `http://localhost:8083/`
2. 点击"打开位置调试工具"按钮

## 功能详解

### 1. 完整视觉预览

#### 节点显示
- **双层图片**：每个节点由底座图片和顶部图片组成，完全还原主组件效果
- **原始比例**：节点图片按原始比例渲染，不会产生拉伸变形
- **缩放控制**：每个节点有独立的scale属性控制缩放比例
- **3D效果**：顶部图片稍微偏移，营造立体感
- **节点名称**：在节点下方显示名称，带阴影效果

#### 管道显示
- **独立管道**：6个管道图片作为独立元素显示，可单独拖拽调整
- **原始比例**：管道图片按原始比例渲染，不会产生拉伸变形
- **缩放控制**：每个管道有独立的scale属性控制缩放比例
- **占位显示**：当管道图片加载失败时，显示半透明蓝色矩形占位
- **调试标签**：每个管道显示名称标签，便于识别

### 2. 拖拽调整功能

#### 基本操作
- **鼠标悬停**：将鼠标悬停在任意节点或管道上，光标会变成手型(grab)
- **开始拖拽**：按住鼠标左键开始拖拽，光标变成抓取状态(grabbing)
- **移动元素**：拖拽过程中节点或管道会跟随鼠标移动
- **释放元素**：松开鼠标左键完成拖拽

#### 拖拽特性
- **双重拖拽**：既可以拖拽节点，也可以拖拽管道图片
- **实时预览**：拖拽过程中可以实时看到位置变化
- **边界限制**：元素不会被拖拽到画布外部
- **高亮显示**：被拖拽的元素会显示绿色边框高亮
- **坐标显示**：拖拽时左上角会显示当前元素名称和坐标
- **独立调整**：节点和管道可以独立调整，互不影响

### 3. 工具栏功能

#### 保存位置到控制台
- **功能**：将当前所有节点和管道的位置信息输出到浏览器控制台
- **格式**：分别输出节点配置和管道配置的JavaScript代码
- **使用**：点击按钮后按F12打开开发者工具查看Console标签页

#### 重置位置
- **功能**：将所有节点和管道恢复到初始位置
- **用途**：当调整出现问题时可以快速重置

#### 显示/隐藏网格
- **功能**：切换50px间距的网格线显示
- **用途**：帮助精确对齐和定位节点

### 4. 辅助功能

#### 网格系统
- **间距**：50像素间距的网格线
- **颜色**：半透明灰色虚线
- **用途**：辅助精确对齐和测量距离

#### 视觉反馈
- **拖拽高亮**：被拖拽的节点显示绿色边框
- **坐标提示**：实时显示拖拽节点的坐标信息
- **鼠标状态**：不同状态下的鼠标光标变化

## 使用流程

### 第一步：打开调试工具
```bash
# 确保项目已启动
npm start

# 访问调试工具
http://localhost:8083/position-debugger
```

### 第二步：调整节点位置
1. **参考设计稿**：对照原始设计稿确定目标位置
2. **拖拽调整**：逐个拖拽节点到理想位置
3. **精确对齐**：使用网格线辅助精确对齐
4. **反复调整**：可以多次调整直到满意

### 第三步：保存配置
1. **点击保存按钮**：点击"保存位置到控制台"
2. **打开控制台**：按F12打开浏览器开发者工具
3. **查看输出**：在Console标签页查看输出的配置代码
4. **复制代码**：选择并复制所有输出的代码

### 第四步：应用到主组件
1. **打开主组件**：编辑 `src/views/SSKJMOMMenu/index.vue`
2. **找到nodes数组**：定位到data中的nodes配置
3. **替换配置**：用复制的代码替换现有的nodes数组内容
4. **保存文件**：保存文件并查看效果

## 输出格式说明

### 控制台输出示例

点击保存按钮后，控制台会分别输出节点和管道的配置代码：

#### 节点配置输出
```javascript
=== 节点位置配置 ===
复制以下代码到原始组件的nodes数组中：

{
    id: 'demand-forecast',
    name: '需求预测',
    x: 150,
    y: 120,
    scale: 1, // 缩放比例，1为原始大小
    // 实际渲染尺寸: 140x100
    topImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-top.png'),
    baseImagePath: require('@/assets/images/SSKJMomMenu/demand-forecast-base.png'),
    menuItems: [
        // 在这里添加菜单项
    ]
},
```

#### 管道配置输出
```javascript
=== 管道位置配置 ===
复制以下代码到原始组件的pipes数组中：

{
    id: 'pipe-1',
    name: '管道1',
    x: 290,
    y: 170,
    scale: 1, // 缩放比例，1为原始大小
    // 实际渲染尺寸: 160x30
    imagePath: require('@/assets/images/SSKJMomMenu/pipe-1.png')
},
```

### 字段说明

#### 节点字段
- **id**: 节点唯一标识符
- **name**: 节点显示名称
- **x, y**: 节点左上角坐标（像素）
- **scale**: 缩放比例（1为原始大小，0.5为缩小一半，2为放大一倍）
- **topImagePath**: 顶部图片路径
- **baseImagePath**: 底座图片路径
- **menuItems**: 菜单项配置（需要手动补充）
- **实际渲染尺寸**: 注释中显示的是图片原始尺寸乘以缩放比例后的实际尺寸

#### 管道字段
- **id**: 管道唯一标识符
- **name**: 管道显示名称
- **x, y**: 管道左上角坐标（像素）
- **scale**: 缩放比例（1为原始大小，0.5为缩小一半，2为放大一倍）
- **imagePath**: 管道图片路径
- **实际渲染尺寸**: 注释中显示的是图片原始尺寸乘以缩放比例后的实际尺寸

## 注意事项

### 坐标系统
- **原点**：左上角为原点(0,0)
- **方向**：向右为X轴正方向，向下为Y轴正方向
- **单位**：坐标单位为像素(px)
- **精度**：坐标会自动四舍五入到整数

### 使用建议
1. **先整体后细节**：先调整整体布局，再精调细节位置
2. **使用网格**：开启网格线辅助精确对齐
3. **多次保存**：调整过程中可以多次保存查看代码
4. **备份原始**：调整前备份原始的节点配置

### 常见问题
1. **节点重叠**：注意避免节点之间重叠
2. **边界溢出**：确保所有节点都在画布范围内
3. **菜单项丢失**：输出的代码中需要手动补充menuItems
4. **图片路径**：确保图片文件存在于指定路径

## 技术细节

### 实现原理
- 使用HTML5 Canvas进行渲染
- 鼠标事件处理实现拖拽功能
- 实时计算和更新节点坐标
- 格式化输出符合要求的配置代码

### 性能优化
- 拖拽时实时重绘Canvas
- 边界检测避免无效操作
- 事件节流优化性能

### 兼容性
- 支持现代浏览器
- 需要Canvas API支持
- 响应式设计适配不同屏幕
