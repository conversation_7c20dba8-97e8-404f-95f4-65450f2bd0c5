/*
 * @Description: 公共请求方法
 * @Author: g<PERSON><PERSON><PERSON>
 * @Date: 2020-09-01 08:44:05
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-06-04 09:53:26
 * @FilePath: \pangea-component\src\utils\requestHttp.js
 */
import axios from "axios";

const token = "7edd8fdf-54e0-4855-9315-c34326c873bb";
const system = "mom";

async function request(url, options, h) {
  // 创建 axios 实例
  const service = axios.create({
    baseURL: "", // api base_url
    timeout: 1000000, // 请求超时时间
  });
  // 请求拦截
  service.interceptors.request.use((config) => {
    if (options && options.body) {
      config.data = options.body;
    }
    config.headers = h
      ? h
      : {
        token,
        Authorization: `Bearer ${token}`,
      };
    config.headers["systemName"] = system;
    // config.headers["plantCode"] = "6210";
    config.headers["content-type"]="application/json"
    return config;
  });
  // 返回拦截
  service.interceptors.response.use((response) => {
    if (response.data.code === "0" || response.data.code === 0) {
      return response.data.data;
    } else {
      return response.data;
    }
  });

  if (window.vm && window.vm.$request) {
    return window.vm.$request(url, options);
  } else {
    return service(url, options);
  }
}
export default request;

export async function pureAxios(options) {
  const service = axios.create({
    baseURL: "",
    timeout: 6000,
  });
  // 请求拦截
  service.interceptors.request.use((config) => {
    config.headers.Authorization = `Bearer ${token}`;
    return config;
  });
  if (window.vm && window.vm.$pureAxios) {
    return window.vm.$pureAxios(options);
  } else {
    return service(options);
  }
}
