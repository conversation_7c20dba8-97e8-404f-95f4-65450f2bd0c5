# 一线员工全过程管理页面

## 功能特性

1. **Canvas渲染系统** - 使用Canvas绘制背景图片、节点图片和连接管道
2. **响应式适配** - 根据视口大小自动缩放图片和调整点击区域
3. **交互系统** - 点击节点显示右上角菜单，支持自定义配置
4. **菜单组件** - 可配置的弹出菜单，支持多种操作
5. **暗色主题支持** - 完整的暗色主题适配

## 图片资源结构

请在 `src/assets/images/SSKJMomMenu/` 目录下创建以下图片资源结构：

```
src/assets/images/SSKJMomMenu/
├── background.png                          # 背景图片
├── demand-forecast-top.png                 # 需求预测节点 - 顶部图片
├── demand-forecast-base.png                # 需求预测节点 - 底座图片
├── recruitment-top.png                     # 人员招聘节点 - 顶部图片
├── recruitment-base.png                    # 人员招聘节点 - 底座图片
├── ai-interview-top.png                    # AI面试节点 - 顶部图片
├── ai-interview-base.png                   # AI面试节点 - 底座图片
├── capability-upgrade-top.png              # 能力升级节点 - 顶部图片
├── capability-upgrade-base.png             # 能力升级节点 - 底座图片
├── entry-training-top.png                  # 入职培训节点 - 顶部图片
├── entry-training-base.png                 # 入职培训节点 - 底座图片
├── performance-management-top.png          # 绩效管理节点 - 顶部图片
├── performance-management-base.png         # 绩效管理节点 - 底座图片
├── efficiency-management-top.png           # 效率管理节点 - 顶部图片
├── efficiency-management-base.png          # 效率管理节点 - 底座图片
├── resignation-management-top.png          # 离职管理节点 - 顶部图片
├── resignation-management-base.png         # 离职管理节点 - 底座图片
├── pipe-1.png                              # 需求预测 -> 人员招聘
├── pipe-2.png                              # 人员招聘 -> AI面试
├── pipe-3.png                              # AI面试 -> 能力升级
├── pipe-4.png                              # 需求预测 -> 入职培训
├── pipe-5.png                              # 入职培训 -> 绩效管理
├── pipe-6.png                              # 绩效管理 -> 效率管理
├── pipe-7.png                              # 能力升级 -> 效率管理
└── pipe-8.png                              # 效率管理 -> 离职管理
```

### 节点图片说明

每个节点由两张图片组成：
- **顶部图片（-top.png）**: 节点的主要图标或内容
- **底座图片（-base.png）**: 节点的底座或背景

绘制时会先绘制底座图片，再在其上方绘制顶部图片，形成层次效果。

## 节点配置

每个节点包含以下配置：

- `id`: 节点唯一标识
- `name`: 节点显示名称
- `x, y`: 节点在Canvas中的位置坐标
- `width, height`: 节点的宽度和高度
- `topImagePath`: 节点顶部图片路径（使用require引入）
- `baseImagePath`: 节点底座图片路径（使用require引入）
- `menuItems`: 右键菜单项配置

### 菜单项配置

每个菜单项包含：
- `label`: 显示文本
- `icon`: Ant Design图标名称
- `action`: 操作标识符

## 使用方法

1. **准备图片资源** - 按照上述结构放置图片文件
2. **配置节点** - 在组件的 `nodes` 数组中配置节点信息
3. **配置连接** - 在组件的 `pipes` 数组中配置节点间的连接关系
4. **自定义菜单** - 修改节点的 `menuItems` 配置自定义菜单

## 响应式特性

- 基于1200px宽度的设计稿进行缩放
- 自动适配不同屏幕尺寸
- 点击区域随缩放比例调整
- 菜单位置自动防止超出视口

## 事件处理

- **点击节点** - 显示右上角菜单
- **鼠标悬停** - 显示节点高亮效果
- **菜单项点击** - 执行对应操作
- **窗口大小改变** - 自动重新计算布局

## 自定义扩展

### 添加新节点

1. 在 `nodes` 数组中添加新节点配置
2. 准备对应的节点图片
3. 配置菜单项

### 添加新连接

1. 在 `pipes` 数组中添加连接配置
2. 准备对应的管道图片

### 自定义操作

在 `handleMenuItemClick` 方法中添加新的 action 处理逻辑。

## 注意事项

1. 图片加载失败时会显示默认样式
2. 支持高DPI屏幕显示
3. 菜单位置会自动调整避免超出屏幕
4. 所有交互都支持触摸设备

## 更新说明

### 已完成的修改

1. **双图片节点结构**: 每个节点现在由两张图片组成（顶部图片和底座图片）
2. **图片路径更新**: 所有图片路径已更改为使用 `src/assets/images/SSKJMomMenu/` 目录
3. **错误处理增强**: 添加了完善的图片加载错误处理，确保即使图片不存在也能正常显示
4. **临时图片配置**: 当前所有节点都临时使用 `demand-forecast.png` 作为测试图片

### 待完成的工作

1. **准备实际图片**: 需要按照文档中的命名规范准备所有节点的顶部图片和底座图片
2. **管道图片**: 需要准备8个连接管道的图片文件
3. **背景图片**: 需要准备页面背景图片
4. **图片优化**: 根据实际图片调整节点的绘制位置和大小比例

### 图片文件清单

需要准备以下图片文件（放置在 `src/assets/images/SSKJMomMenu/` 目录下）：

**背景图片:**
- background.png

**节点图片（16个文件）:**
- demand-forecast-top.png / demand-forecast-base.png
- recruitment-top.png / recruitment-base.png
- ai-interview-top.png / ai-interview-base.png
- capability-upgrade-top.png / capability-upgrade-base.png
- entry-training-top.png / entry-training-base.png
- performance-management-top.png / performance-management-base.png
- efficiency-management-top.png / efficiency-management-base.png
- resignation-management-top.png / resignation-management-base.png

**管道图片（8个文件）:**
- pipe-1.png 到 pipe-8.png

### 使用方法

1. 将所有图片文件放置到指定目录
2. 根据需要调整节点的位置、大小和图片绘制比例
3. 自定义菜单项和操作逻辑
4. 测试响应式效果和交互功能
