<!--
 * @Author: othniel <EMAIL>
 * @Date: 2025-05-13 16:01:51
 * @LastEditors: othniel <EMAIL>
 * @LastEditTime: 2025-06-03 09:40:57
 * @FilePath: \pangea-component\src\views\DMSBIPageReview\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="workshop-meeting" :class="themeClasses">
    <div class="main-container">
      <!-- 左侧主要内容区域 -->
      <div class="content-area" :class="{ 'content-area-with-sidebar': aiDialogVisible }">
        <!-- 回到顶部按钮 -->
        <div class="back-to-top" v-if="showBackToTop" @click="scrollToTop">
          <a-icon type="arrow-up" />
        </div>
        <!-- 顶部标题和筛选条件 -->
        <div class="header">
          <!-- TODO:只有在大屏展示情况下才展示banner -->
          <div class="title" v-if="onScreenCasting">
            <div class="title-text">车间站立会议</div>
          </div>
          <div class="filter-container">
            <div class="filter-wrapper" :class="{ 'with-bg': !onScreenCasting && !darkTheme }">
              <div class="filter-items">
                <div class="filter-item-container">
                  <a-date-picker v-model="searchForm.date" :format="dateFormat" class="filter-item" placeholder="选择日期"
                    :value-format="dateFormat" />
                </div>
                <div class="filter-item-container">
                  <!-- header select -->
                  <OrgStructureSelect v-model="searchForm" @init-complete="handleOrgInitComplete" />
                  <!-- <button @click="handleGETData">测试请求</button> -->
                </div>
              </div>
            </div>
            <div class="button-group" :class="{ 'with-bg': !onScreenCasting && !darkTheme }">
              <a-button class="ai_btn" @click="aiDialogVisible = true">
                <img src="~@/assets/images/workShop_standing_meeting/ai_icon.png" class="ai-icon" />
                AI总结
              </a-button>
              <a-button class="QRCode_btn" @click="QRCodeVisible = true">
                <img src="~@/assets/images/workShop_standing_meeting/QRCode_icon.png" class="ai-icon"
                  v-if="!darkTheme" />
                <img src="~@/assets/images/workShop_standing_meeting/QRCode_light.png" class="ai-icon" v-else />
                会议签到</a-button>
              <a-popover placement="bottomLeft">
                <template slot="content">
                  <!-- <div class="popover-item">
                    <div>页面配置</div>
                    <a-icon type="right" />
                  </div> -->
                  <div class="popover-item">
                    <div>深色背景</div>
                    <a-switch size="small" v-model="darkTheme" @change="handleThemeChange" />
                  </div>
                  <!-- <div class="popover-item">
                    <div>投屏</div>
                    <a-icon type="right" class="popoverSwitch" />
                  </div> -->
                  <!-- <div class="popover-item">
                    <div>推送</div>
                    <a-icon type="right" />
                  </div> -->
                </template>
                <a-button class="moreOperator_btn">
                  更多
                  <a-icon type="down" class="moreOperator_icon" :style="{ color: darkTheme ? '#ffffff' : '#1d2129' }" />
                </a-button>
              </a-popover>
            </div>
          </div>
        </div>

        <div class="main-content">
          <!-- 上部区域 -->
          <div class="top-charts">
            <!-- 交付-日生产计划执行率 -->
            <div class="card-container full-card">
              <div class="card-title">
                <span class="title-icon"></span>
                <span class="card-title-text">交付-日生产计划执行率（机型）</span>
                <!-- 添加滚动文本组件 -->
                <ScrollingText :text="chartTexts.delivery" :showScroll="hasDeliveryData" :darkTheme="darkTheme"
                  :isQualityOrLostTime="false" class="chart-scrolling-text" />
              </div>
              <div class="card-content">
                <div class="overview">
                  <div class="quality-item">
                    <div class="item-value">{{ deliver.totalProductSchedule }}</div>
                    <div class="item-label">当日计划产量</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ deliver.totalProductActual }}</div>
                    <div class="item-label">当日实际产量</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ deliver.productionProgress }}%</div>
                    <div class="item-label">当日计划达成率</div>
                  </div>
                </div>
                <div class="chart-container">
                  <div ref="deliveryChart" class="chart"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- 下部区域 -->
          <div class="bottom-charts">
            <!-- 质量概览 -->
            <div class="card-container">
              <div class="card-title">
                <span class="title-icon"></span>
                <span class="card-title-text">质量</span>
                <!-- 添加滚动文本组件 -->
                <ScrollingText :text="getQualityScrollText" :showScroll="hasQualityData" :darkTheme="darkTheme"
                  :isQualityOrLostTime="true" class="chart-scrolling-text" />
                <div class="quality-tab-btns">
                  <a-button v-for="btn in visibleQualityButtons" :key="btn.key" type="primary" size="small"
                    :class="[`${btn.key}Btn`, 'operatorBtnItem', { active: validActiveQualityBtn === btn.key }]"
                    @click="setActiveQualityBtn(btn.key)">
                    <div>{{ btn.label }}</div>
                  </a-button>
                </div>
              </div>
              <div class="card-content">
                <!-- 直通率的三个展示 -->
                <div class="overview" v-if="validActiveQualityBtn === 'FTY'">
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.fty.totalDefectQty || 0 }}</div>
                    <div class="item-label">当日不良数</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.fty.totalProductQty || 0 }}</div>
                    <div class="item-label">当日产量</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.fty.totalPassThroughRate || 0 }}%</div>
                    <div class="item-label">当日良品率</div>
                  </div>
                </div>
                <!-- OQC的三个展示 -->
                <div class="overview" v-if="validActiveQualityBtn === 'OQC'">
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.OQC.rejectQty }}</div>
                    <div class="item-label">当前不良数</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.OQC.outputQty }}</div>
                    <div class="item-label">当前抽检总数</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.OQC.oqcRatio }}</div>
                    <div class="item-label">抽检不良率</div>
                  </div>
                </div>
                <!-- 焊接泄露率 -->
                <div class="overview" v-if="validActiveQualityBtn === 'WELDING'">
                  <div class=" quality-item">
                    <div class="item-value">{{ quality_overview.WELDING.currentLeakCount || 0 }}</div>
                    <div class="item-label">当前泄露数</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.WELDING.currentProduction || 0 }}</div>
                    <div class="item-label">当前产量</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ quality_overview.WELDING.currentLeakRate || 0 }}%</div>
                    <div class="item-label">当前泄露率</div>
                  </div>
                </div>
                <div class="chart-container">
                  <div ref="qualityChart" class="chart"></div>
                </div>
              </div>
            </div>
            <!-- 工时损失 -->
            <div class="card-container">
              <div class="card-title">
                <span class="title-icon"></span>
                <span class="card-title-text">工时损失</span>
                <!-- 添加滚动文本组件 -->
                <ScrollingText :text="getLostScrollText" :showScroll="hasTimeData" :darkTheme="darkTheme"
                  :isQualityOrLostTime="true" class="chart-scrolling-text" />
                <div class="lost-tab-btns">
                  <a-button v-for="btn in visibleLostButtons" :key="btn.key" type="primary" size="small"
                    :class="[`${btn.key}Btn`, 'operatorBtnItem', { active: validActiveLostBtn === btn.key }]"
                    @click="setActiveLostBtn(btn.key)">
                    <div>{{ btn.label }}</div>
                  </a-button>
                </div>
              </div>
              <div class="card-content">
                <div class="overview" v-if="validActiveLostBtn === 'stop_production'">
                  <div class="quality-item">
                    <div class="item-value">{{ lost.stop_production.currentDowntimeDuration }}</div>
                    <div class="item-label">当前停产时间</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ lost.stop_production.currentDowntimeCount }}</div>
                    <div class="item-label">当前停产次数</div>
                  </div>
                </div>
                <div class="overview" v-if="validActiveLostBtn === 'fault'">
                  <div class="quality-item">
                    <div class="item-value">{{ lost.fault.totalLostTime }}</div>
                    <div class="item-label">当前停产时间</div>
                  </div>
                  <div class="quality-item">
                    <div class="item-value">{{ lost.fault.totalLostNum }}</div>
                    <div class="item-label">当前停产次数</div>
                  </div>
                </div>
                <div class="chart-container">
                  <div ref="timeChart" class="chart"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 异常问题跟进 -->
        <div class="card-container table-container">
          <div class="card-title">
            <span class="title-icon"></span>
            <span class="card-title-text">异常问题跟进</span>
            <a-button size="small" class="addExceptionBtn" @click="handleOpenDialog"><span
                class="addExceptionBtnText">新建事件</span></a-button>
          </div>
          <div class="card-content">
            <div class="issue-overview">
              <div class="issue-item">
                <div class="item-value">{{ eventIssues.totalCount }}</div>
                <div class="item-label">今日事件总数</div>
              </div>
              <div class="issue-item">
                <div class="item-value">{{ eventIssues.unCompleteCount }}</div>
                <div class="item-label">进行中事件</div>
              </div>
              <div class="issue-item">
                <div class="item-value">{{ eventIssues.completedCount }}</div>
                <div class="item-label">已闭环事件</div>
              </div>
            </div>
            <a-table :columns="columns" :dataSource="paginatedTableData" :pagination="pagination" rowKey="id"
              :loading="tableLoading" size="large" :rowClassName="() => 'table-row'" @change="handleTableChange">
              <template slot="action" slot-scope="text, record">
                <a-button type="link" size="small" @click="tableHandleUpgrade(record)">升级</a-button>
              </template>
              <template slot="currentState" slot-scope="text, record">
                <div class="category-item" :class="getCategoryClass(record.currentState)">
                  <span class="category-text">{{ getStatusText(record.currentState) }}</span>
                </div>
              </template>
              <template slot="eventSource" slot-scope="text, record">
                <span class="category-text">{{ getEventSourceText(record.eventSource) }}</span>
              </template>
              <template slot="eventStatus" slot-scope="text, record">
                <span class="category-text">{{ record.eventStatus == 1 ? '进行中' : '已完成' }}</span>
              </template>
            </a-table>
          </div>
        </div>

        <!-- AI总结采纳 -->
        <div class="card-container table-container" v-if="aiSummarize.length">
          <div class="card-title">
            <span class="title-icon"></span>
            <span class="card-title-text">AI总结</span>
          </div>
          <div class="card-content">
            <p style="font-weight: bold;" :class="{ 'dark-text': darkTheme }">以下是当前页面AI总结：</p>
            <div class="ai-summary-content" v-for="(item, index) in aiSummarize" :key="index"
              :class="{ 'dark-content': darkTheme }">
              <div v-if="item.isMarkdown" v-html="renderMarkdown(item.content)" class="markdown-content"
                :class="{ 'dark-markdown': darkTheme }"></div>
              <div v-else :class="{ 'dark-text': darkTheme }">{{ item.content }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧AI对话区域 -->
      <div class="ai-sidebar" :class="{ visible: aiDialogVisible }" v-show="aiDialogVisible">
        <div class="ai-header">
          <div class="ai-header-left">
            <img src="@/assets/images/workShop_standing_meeting/ai_avatar.png" alt="ai_avatar" class="ai-avatar" />
            <span class="ai-title">AI总结</span>

            <div class="ai-header-right">
              <a-icon type="close" @click="aiDialogVisible = false" class="close-icon" />
            </div>
          </div>
        </div>
        <AiConversation :dark-theme="darkTheme" @pushToSummarize="pushToSummarize" :date="this.searchForm.date"
          :searchForm="{
            ...this.searchForm
          }" :isInitializingOrg="isInitializingOrg" :visible="aiDialogVisible" />
      </div>
    </div>

    <!-- 二维码模态框组件 -->
    <QRCodeModal
      :visible.sync="QRCodeVisible"
      :darkTheme="darkTheme"
      :searchForm="searchForm"
    />
    <!-- 模态框 - 新增事件 -->
    <AddEventDialog :visible.sync="isDialogVisible" :darkTheme="darkTheme" :allDictsData="allDictsData"
      :searchForm="searchForm" @success="fetchTableData" @cancel="handleDialogCancel"
      @dialog-opened="hideAllTooltips" />

    <!-- echarts时段数据详情对话框 -->
    <DetailDataDialog ref="detailDataDialog" :darkTheme="darkTheme" @dialog-opened="hideAllTooltips" />
  </div>
</template>

<script>
import moment from "moment";
// 导入所有echarts相关功能
import {
  // 图表初始化
  initQualityChart,
  initDeliveryChart,
  initTimeChart,

  // tooltip管理
  initAutoTooltip,
  startAutoTooltip,
  stopAutoTooltip,
  pauseAutoTooltip,
  resumeAutoTooltip,
  resetAutoTooltip,

  // 图表工具函数
  checkChartHasData,
  setupChartMouseEvents,
  cleanupChartMouseEvents,
  setupUserActivityListeners,
  removeUserActivityListeners,
  resetChartTooltip,

} from "./echarts";

import { tableColumns, getTableData } from "./table";
import ComFunMixins from "pangea-com/lib/ComFunMixins";
import "./style/darkThemeCoverage.css"; // 直接导入CSS文件，而不是动态导入
import "./style/lightThemeFix.css"; // 导入亮色主题修复样式文件
import AiConversation from "./AiConversation";
import { marked } from 'marked'; // 导入marked库用于渲染markdown
import { getAllDictsData, queryWorkOrderDataUpgrade, getAllFactory } from "./Api";
import ScrollingText from './components/ScrollingText.vue'; // 导入滚动文本组件
import DetailDataDialog from './components/DetailDataDialog.vue'; // 导入详情对话框组件
import OrgStructureSelect from './components/OrgStructureSelect.vue'; // 导入组织结构选择组件
import AddEventDialog from './components/AddEventDialog.vue'; // 导入新增事件对话框组件
import QRCodeModal from './components/QRCodeModal.vue'; // 导入二维码模态框组件
import { createChartDataManager } from './utils/ChartDataManager'; // 导入图表数据管理器


export default {
  mixins: [ComFunMixins],
  components: {
    AiConversation,
    ScrollingText,
    DetailDataDialog,
    OrgStructureSelect,
    AddEventDialog,
    QRCodeModal
  },
  data() {
    return {
      // 页面基础配置
      onScreenCasting: false, // 是否投屏
      darkTheme: false, // 是否深色主题
      darkCssLoaded: true, // 已将CSS直接导入，因此始终为true
      QRCodeVisible: false, // QRCode模态框是否可见
      showBackToTop: false, // 是否显示回到顶部按钮
      dateFormat: "YYYY-MM-DD",
      dateFormatTime: "YYYY-MM-DD HH:mm:ss",
      currentUser: "xiaofan.xu",
      // AI对话相关数据
      aiDialogVisible: false,
      // 搜索表单数据，用于筛选图表和表格数据
      searchForm: {
        date: moment().format('YYYY-MM-DD'),
        baseName: '', // 基地名称 海信空调公司
        factoryName: '', // 工厂名称 空调公司平度工厂
        branchName: '', // 分厂名称 家空分厂
        workshopName: '', // 车间名称 内机组装
        teamName: '', // 班组名称 A内1
        lineName: "", //线体名称 A内
        // 保留code字段用于内部逻辑处理
        baseCode: '', //KTGS
        lineCode: "", //6210-ZZ-PA
        factoryCode: '', //6210
        branchCode: '', //H011080
        workshopCode: '', //6210-NJZZ
        teamCode: '' //CM_TREE003147
      },
      // 组织层级数据
      orgLists: {
        baseList: [{
          name: '海信空调公司',
          factoryModelCode: 'KTGS',
        }, {
          name: '海信洗衣机公司',
          factoryModelCode: 'XYJGS',
        }], // 基地列表
        factoryList: [], // 工厂列表
        branchList: [], // 分厂列表
        workshopList: [], // 车间列表
        teamList: [] // 班组列表
      },
      // 组织层级初始化标志
      isInitializingOrg: true,
      activeQualityBtn: "FTY", // 默认选中直通率按钮
      qualityChart: null,
      deliveryChart: null,
      timeChart: null,
      columns: tableColumns,
      tableData: [],
      tableLoading: false, // 表格加载状态
      // 分页相关配置
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条记录`,
        pageSizeOptions: ['5', '10', '20', '50'],
      },
      isDialogVisible: false, // 模态框是否可见
      aiSummarize: [],//ai总结
      allDictsData: {},//获取字典值：在新增事件中使用
      quality_overview: {
        fty: {
          totalDefectQty: 0,//当日不良数
          totalProductQty: 0,//当日产量
          totalPassThroughRate: 0//当日良品率
        },
        OQC: {
          rejectQty: 0,//当前不良数
          outputQty: 0, //当前抽检总数
          oqcRatio: 0, //抽检不良率
        },
        WELDING: {
          currentLeakCount: 0, // 当前泄露数
          currentProduction: 0, // 当前产量
          currentLeakRate: 0 // 当前泄露率
        }
      },
      deliver: {
        totalProductSchedule: "",
        totalProductActual: "",
        productionProgress: ""
      },
      activeLostBtn: "stop_production",
      lost: {
        stop_production: {
          currentDowntimeDuration: 0,//当前停产时间
          currentDowntimeCount: 0//当前停产次数
        },
        fault: {
          totalLostTime: 0,//总损失时间
          totalLostNum: 0//总停线次数
        }
      },
      // tooltip自动滚动相关
      autoTooltip: {
        enabled: true, // 是否启用自动滚动
        deliveryTooltip: null, // 交付图表tooltip控制器
        qualityTooltip: null, // 质量图表tooltip控制器
        timeTooltip: null, // 工时损失图表tooltip控制器
        inactivityTimer: null, // 用户无操作计时器
        inactivityTimeout: 3000, // 无操作超时时间(毫秒)，默认3秒
        isUserActive: false // 用户是否处于活动状态
      },
      eventIssues: {
        totalCount: 0,
        unCompleteCount: 0,
        completedCount: 0
      },
      // 图表数据滚动文本
      chartTexts: {
        delivery: '', // 交付图表滚动文本
        quality: {
          fty: '', // 直通率滚动文本
          oqc: '', // OQC抽检不良率滚动文本
          welding: '' // 焊接泄漏率滚动文本
        },
        lost: {
          stop_production: '', // 停产损失时间滚动文本
          fault: '' // 过站损失时间滚动文本
        }
      },
      // 图表数据是否有效标志
      hasDeliveryData: false,
      hasQualityData: false,
      hasTimeData: false,
      tableFilters: [],
      //mom相关信息
      momUser: {
        userLoginName: "", userName: "", token: "",//用户个人信息
      },
      // 图表自动刷新相关
      chartRefreshTimer: null, // 图表自动刷新定时器
      chartRefreshInterval: 5 * 60 * 1000, // 5分钟刷新间隔（毫秒）
      isChartRefreshEnabled: true, // 是否启用图表自动刷新

      // 按钮配置系统
      buttonConfigs: {
        quality: {
          FTY: {
            key: 'FTY',
            label: '直通率',
            visible: () => true // 始终显示
          },
          OQC: {
            key: 'OQC',
            label: 'OQC抽检不良率',
            visible: () => true // 始终显示
          },
          WELDING: {
            key: 'WELDING',
            label: '焊接泄漏率',
            visible: () => this.searchForm.baseCode !== 'XYJGS' // 根据baseCode判断
          }
        },
        lost: {
          stop_production: {
            key: 'stop_production',
            label: '停产损失时间',
            visible: () => this.searchForm.baseCode !== 'XYJGS' // 根据baseCode判断
          },
          fault: {
            key: 'fault',
            label: '过站损失时间',
            visible: () => true // 始终显示
          }
        }
      },

      // 图表数据管理器
      chartDataManager: null
    };
  },
  computed: {
    themeClasses() {
      return {
        "dark-theme": this.darkTheme,
      };
    },

    // 获取可见的质量按钮列表
    visibleQualityButtons() {
      // 在初始化期间，返回所有按钮，避免触发不必要的计算
      if (this.isInitializingOrg) {
        return Object.values(this.buttonConfigs.quality);
      }
      return Object.values(this.buttonConfigs.quality).filter(btn => btn.visible());
    },

    // 获取可见的工时损失按钮列表
    visibleLostButtons() {
      // 在初始化期间，返回所有按钮，避免触发不必要的计算
      if (this.isInitializingOrg) {
        return Object.values(this.buttonConfigs.lost);
      }
      return Object.values(this.buttonConfigs.lost).filter(btn => btn.visible());
    },

    // 获取有效的质量按钮选择（如果当前选中的按钮被隐藏，则选择第一个可见的按钮）
    validActiveQualityBtn() {
      // 在初始化期间，直接返回当前选中的按钮，避免触发不必要的计算
      if (this.isInitializingOrg) {
        return this.activeQualityBtn;
      }

      const currentBtn = this.buttonConfigs.quality[this.activeQualityBtn];
      if (currentBtn && currentBtn.visible()) {
        return this.activeQualityBtn;
      }
      // 如果当前选中的按钮被隐藏，返回第一个可见按钮的key
      const firstVisibleBtn = this.visibleQualityButtons[0];
      return firstVisibleBtn ? firstVisibleBtn.key : 'FTY';
    },

    // 获取有效的工时损失按钮选择（如果当前选中的按钮被隐藏，则选择第一个可见的按钮）
    validActiveLostBtn() {
      // 在初始化期间，直接返回当前选中的按钮，避免触发不必要的计算
      if (this.isInitializingOrg) {
        return this.activeLostBtn;
      }

      const currentBtn = this.buttonConfigs.lost[this.activeLostBtn];
      if (currentBtn && currentBtn.visible()) {
        return this.activeLostBtn;
      }
      // 如果当前选中的按钮被隐藏，返回第一个可见按钮的key
      const firstVisibleBtn = this.visibleLostButtons[0];
      return firstVisibleBtn ? firstVisibleBtn.key : 'fault';
    },
    // 根据当前分页设置计算要显示的表格数据
    paginatedTableData() {
      // 处理不同的数据结构情况
      let dataArray = [];

      if (Array.isArray(this.tableData)) {
        // 如果tableData本身是数组
        dataArray = this.tableData;
      } else if (this.tableData && this.tableData.data) {
        // 如果tableData是对象且有data属性
        if (Array.isArray(this.tableData.data)) {
          // 如果data是数组
          dataArray = this.tableData.data;
        } else if (this.tableData.data.rows && Array.isArray(this.tableData.data.rows)) {
          // 如果data.rows是数组
          dataArray = this.tableData.data.rows;
        }
      }

      // 如果我们使用服务器端分页，则直接返回数据数组，不需要在前端进行切片
      return dataArray;
    },
    // 获取质量图表滚动文本
    getQualityScrollText() {
      // 根据当前有效的质量按钮类型返回对应的滚动文本
      if (this.validActiveQualityBtn === 'FTY') {
        return this.chartTexts.quality.fty;
      } else if (this.validActiveQualityBtn === 'OQC') {
        return this.chartTexts.quality.oqc;
      } else if (this.validActiveQualityBtn === 'WELDING') {
        return this.chartTexts.quality.welding;
      }
      return '';
    },
    // 获取工时损失图表滚动文本
    getLostScrollText() {
      // 根据当前有效的工时损失按钮类型返回对应的滚动文本
      if (this.validActiveLostBtn === 'stop_production') {
        return this.chartTexts.lost.stop_production;
      } else if (this.validActiveLostBtn === 'fault') {
        return this.chartTexts.lost.fault;
      }
      return '';
    }
  },
  async created() {
    // 初始化搜索表单的默认值
    await this.initSearchFormDefaults();
  },
  async mounted() {
    // 设置初始化标志为true，防止在初始化过程中触发不必要的请求
    this.isInitializingOrg = true;

    // 初始化图表数据管理器
    this.chartDataManager = createChartDataManager(this);

    // 初始化图表和字典数据
    this.initCharts();
    await this.fetchAllDictsData();

    // 获取mom用户的信息
    this.momUser.userLoginName = this.$store.state.user.info.loginName //用户ldap账号
    this.momUser.userName = this.$store.state.user.info.userName//用户名称
    this.momUser.token = this.$store.state.user.token//用户token

    // 确保暗色主题样式正确应用
    if (this.darkTheme) {
      document.body.classList.add("has-dark-theme");
    }

    window.addEventListener("resize", this.handleResize);

    // 添加内容区域滚动事件监听
    this.$nextTick(() => {
      const contentArea = document.querySelector('.content-area');
      if (contentArea) {
        // 移除可能已存在的事件监听，防止重复绑定
        contentArea.removeEventListener("scroll", this.handleScroll);
        // 重新添加事件监听
        contentArea.addEventListener("scroll", this.handleScroll);
        // 初始检查是否需要显示回到顶部按钮
        this.handleScroll();

        // 确保初始状态下正确设置
        setTimeout(() => {
          this.handleScroll();
          // 初始化按钮位置
          this.updateBackToTopPosition();
        }, 500);
      }
    });

    // 等待所有图表数据加载完成后，初始化tooltip自动滚动
    setTimeout(() => {
      this.initAutoTooltip();

      // 添加用户活动监听
      this.setupUserActivityListeners();

      // 启动自动滚动
      if (this.autoTooltip.enabled) {
        this.startAutoTooltip();
      }

      // 启动图表自动刷新定时器
      this.startChartRefreshTimer();
    }, 2000); // 延迟2秒，确保图表数据已加载
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);

    // 移除内容区域滚动事件监听
    const contentArea = document.querySelector('.content-area');
    if (contentArea) {
      contentArea.removeEventListener("scroll", this.handleScroll);
    }
    this.disposeCharts();

    // 清理tooltip自动滚动相关资源
    this.cleanupAutoTooltip();

    // 移除用户活动监听
    this.removeUserActivityListeners();

    // 清理图表自动刷新定时器
    this.clearChartRefreshTimer();

    // 移除暗色主题相关类
    document.body.classList.remove("has-dark-theme");
  },
  watch: {
    darkTheme(newVal) {
      // 监听暗色主题变化，确保样式正确应用
      if (newVal) {
        document.body.classList.add("has-dark-theme");
      } else {
        document.body.classList.remove("has-dark-theme");
      }
    },
    // 添加对aiDialogVisible的监听
    aiDialogVisible() {
      // 给图表一点时间完成过渡动画后再resize
      setTimeout(() => {
        if (this.qualityChart) this.qualityChart.resize();
        if (this.deliveryChart) this.deliveryChart.resize();
        if (this.timeChart) this.timeChart.resize();

        // 更新回到顶部按钮位置
        this.updateBackToTopPosition();

        // 重置tooltip自动滚动
        this.resetAutoTooltip();
      }, 300);
    },
    // 监听自动tooltip开关
    'autoTooltip.enabled'(newVal) {
      if (newVal) {
        this.startAutoTooltip();
      } else {
        stopAutoTooltip(this.autoTooltip);
      }
    },
    // 监听搜索表单变化
    searchForm: {
      handler(newVal, oldVal) {
        // 如果处于初始化阶段，不执行后续操作
        if (this.isInitializingOrg) {
          console.log('组织层级初始化中，跳过搜索表单变化处理');
          return;
        }

        console.log('搜索表单变化，更新所有图表数据', {
          date: {
            new: newVal.date,
            old: oldVal.date,
            changed: newVal.date !== oldVal.date
          },
          org: {
            new: `${newVal.baseName}-${newVal.factoryName}-${newVal.branchName}-${newVal.workshopName}-${newVal.teamName}`,
            old: `${oldVal.baseName}-${oldVal.factoryName}-${oldVal.branchName}-${oldVal.workshopName}-${oldVal.teamName}`,
          }
        });

        // 使用图表数据管理器更新所有图表数据
        if (this.chartDataManager) {
          this.chartDataManager.updateAllCharts(this.searchForm);
        }

        // 重置tooltip自动滚动
        this.resetAutoTooltip();

        // 重置图表自动刷新定时器
        this.resetChartRefreshTimer();
      },
      deep: true
    },
    // 监听质量按钮变化
    activeQualityBtn() {
      // 在初始化期间不执行图表更新
      if (this.isInitializingOrg) {
        console.log('组织层级初始化中，跳过质量按钮变化处理');
        return;
      }

      // 使用图表数据管理器更新质量图表
      this.updateQualityChart();
      // 重置图表自动刷新定时器
      this.resetChartRefreshTimer();
    },
    // 监听工时损失按钮变化
    activeLostBtn() {
      // 在初始化期间不执行图表更新
      if (this.isInitializingOrg) {
        console.log('组织层级初始化中，跳过工时损失按钮变化处理');
        return;
      }

      // 使用图表数据管理器更新工时损失图表
      this.updateLostChart();
      // 重置图表自动刷新定时器
      this.resetChartRefreshTimer();
    },

    // 监听有效的质量按钮变化
    validActiveQualityBtn(newVal, oldVal) {
      // 在初始化期间不执行同步操作
      if (this.isInitializingOrg) {
        return;
      }

      if (newVal !== oldVal) {
        // 如果有效按钮发生变化，同步更新activeQualityBtn
        if (this.activeQualityBtn !== newVal) {
          console.log(`有效质量按钮变化: ${oldVal} -> ${newVal}，同步更新activeQualityBtn`);
          this.activeQualityBtn = newVal;
        }
      }
    },

    // 监听有效的工时损失按钮变化
    validActiveLostBtn(newVal, oldVal) {
      // 在初始化期间不执行同步操作
      if (this.isInitializingOrg) {
        return;
      }

      if (newVal !== oldVal) {
        // 如果有效按钮发生变化，同步更新activeLostBtn
        if (this.activeLostBtn !== newVal) {
          console.log(`有效工时损失按钮变化: ${oldVal} -> ${newVal}，同步更新activeLostBtn`);
          this.activeLostBtn = newVal;
        }
      }
    },

  },
  methods: {
    /**
     * 初始化搜索表单的默认值
     * 优先从localStorage获取orgArray，如果没有则尝试获取company和plantCode
     */
    async initSearchFormDefaults() {
      try {
        // 优先级1: 获取orgArray，包含四个组织层级信息
        const orgArrayStr = window.localStorage.getItem('orgArray');
        if (orgArrayStr) {
          console.log('从localStorage获取orgArray:', orgArrayStr);
          const orgArray = JSON.parse(orgArrayStr);

          // orgArray包含四个信息：[baseCode, factoryCode, workshopCode, lineCode]
          if (Array.isArray(orgArray) && orgArray.length >= 4) {
            this.searchForm.baseCode = orgArray[0] || '';
            this.searchForm.factoryCode = orgArray[1] || '';
            this.searchForm.workshopCode = orgArray[2] || '';
            this.searchForm.lineCode = orgArray[3] || '';

            console.log('设置orgArray默认值:', {
              baseCode: this.searchForm.baseCode,
              factoryCode: this.searchForm.factoryCode,
              workshopCode: this.searchForm.workshopCode,
              lineCode: this.searchForm.lineCode
            });

            // 当四个值都有时，需要补全可能缺失的分厂层级
            if (this.searchForm.baseCode && this.searchForm.factoryCode &&
              this.searchForm.workshopCode && this.searchForm.lineCode) {
              await this.completeOrgChain();
            }

            return; // 成功获取orgArray，直接返回，不再执行后续逻辑
          }
        }

        // 优先级2: 如果没有获取到orgArray，尝试获取plantCode和company
        const plantCode = window.localStorage.getItem('plantCode');
        const company = window.localStorage.getItem('company');

        if (plantCode) {
          console.log('未获取到orgArray，但获取到plantCode:', plantCode);
          this.searchForm.factoryCode = plantCode;

          // 如果能获取到plantCode，company也一定存在，同时设置
          if (company) {
            this.searchForm.baseCode = company;
            console.log('同时设置company:', company);
          }
          return; // 成功获取plantCode，直接返回，不再执行后续逻辑
        }

        // 优先级3: 如果没有获取到plantCode，但获取到了company
        if (company) {
          console.log('未获取到orgArray和plantCode，但获取到company:', company);
          this.searchForm.baseCode = company;
          return; // 成功获取company，直接返回，不再执行后续逻辑
        }

        // 优先级4: 如果都没有获取到，使用硬编码的默认值
        console.log('未获取到任何localStorage数据，使用默认值');
        this.searchForm.baseName = '海信空调公司';
        this.searchForm.baseCode = 'KTGS';
        this.searchForm.factoryCode = '6210';

      } catch (error) {
        console.error('初始化搜索表单默认值失败:', error);
        // 发生错误时使用硬编码的默认值
        this.searchForm.baseName = '海信空调公司';
        this.searchForm.baseCode = 'KTGS';
        this.searchForm.factoryCode = '6210';
      }
    },

    /**
     * 补全组织链条
     * 当orgArray四个值都有时，通过下级反查上级来补全可能缺失的分厂层级
     */
    async completeOrgChain() {
      try {
        // 使用baseCode获取完整的组织层级数据
        const result = await getAllFactory(this.searchForm.baseCode);
        if (!result) {
          console.error('获取组织层级数据失败');
          return;
        }

        // 处理API返回的数据结构
        let orgData;
        if (typeof result === 'object') {
          if (result.data) {
            orgData = result.data;
          } else {
            orgData = result;
          }
        } else {
          console.error('获取组织层级数据失败: result 不是对象', result);
          return;
        }

        // 检查数据完整性
        if (!orgData.factory || !orgData.subfactory || !orgData.workshop || !orgData.line || !orgData.team) {
          console.error('组织数据结构不完整', orgData);
          return;
        }

        // 通过车间code反查分厂code
        if (this.searchForm.workshopCode && !this.searchForm.branchCode) {
          const workshop = orgData.workshop.find(item => item.factoryModelCode === this.searchForm.workshopCode);
          if (workshop && workshop.parentCode) {
            this.searchForm.branchCode = workshop.parentCode;

            // 通过分厂code找到分厂名称
            const branch = orgData.subfactory.find(item => item.factoryModelCode === workshop.parentCode);
            if (branch) {
              this.searchForm.branchName = branch.factoryModelName;
              console.log(`通过车间反查到分厂: ${branch.factoryModelName} (${workshop.parentCode})`);
            }
          }
        }

        // 通过工厂code找到工厂名称
        if (this.searchForm.factoryCode && !this.searchForm.factoryName) {
          const factory = orgData.factory.find(item => item.factoryModelCode === this.searchForm.factoryCode);
          if (factory) {
            this.searchForm.factoryName = factory.factoryModelName;
            console.log(`找到工厂名称: ${factory.factoryModelName}`);
          }
        }

        // 通过车间code找到车间名称
        if (this.searchForm.workshopCode && !this.searchForm.workshopName) {
          const workshop = orgData.workshop.find(item => item.factoryModelCode === this.searchForm.workshopCode);
          if (workshop) {
            this.searchForm.workshopName = workshop.factoryModelName;
            console.log(`找到车间名称: ${workshop.factoryModelName}`);
          }
        }

        // 通过线体code找到线体名称
        if (this.searchForm.lineCode && !this.searchForm.lineName) {
          const line = orgData.line.find(item => item.factoryModelCode === this.searchForm.lineCode);
          if (line) {
            this.searchForm.lineName = line.factoryModelName;
            console.log(`找到线体名称: ${line.factoryModelName}`);
          }
        }

        // 通过基地code找到基地名称
        if (this.searchForm.baseCode && !this.searchForm.baseName) {
          // 基地信息是硬编码的，根据baseCode匹配
          const baseMapping = {
            'KTGS': '海信空调公司',
            'XYJGS': '海信洗衣机公司'
          };
          if (baseMapping[this.searchForm.baseCode]) {
            this.searchForm.baseName = baseMapping[this.searchForm.baseCode];
            console.log(`找到基地名称: ${baseMapping[this.searchForm.baseCode]}`);
          }
        }

        console.log('组织链条补全完成:', {
          baseName: this.searchForm.baseName,
          baseCode: this.searchForm.baseCode,
          factoryName: this.searchForm.factoryName,
          factoryCode: this.searchForm.factoryCode,
          branchName: this.searchForm.branchName,
          branchCode: this.searchForm.branchCode,
          workshopName: this.searchForm.workshopName,
          workshopCode: this.searchForm.workshopCode,
          lineName: this.searchForm.lineName,
          lineCode: this.searchForm.lineCode
        });

        // 强制更新视图，确保组织层级组件能够响应数据变化
        this.$nextTick(() => {
          this.$forceUpdate();
        });

      } catch (error) {
        console.error('补全组织链条失败:', error);
      }
    },

    /**
     * 处理组织层级初始化完成事件
     * 当组织结构组件初始化完成后，开始获取图表数据
     */
    async handleOrgInitComplete() {
      console.log('组织结构组件初始化完成，开始获取数据');

      // 延迟一点时间，确保组织结构组件的数据已经同步到父组件
      setTimeout(async () => {
        // 初始化完成，设置标志为false
        this.isInitializingOrg = false;

        // 初始化表格数据
        await this.initTableData();

        // 获取所有图表数据
        if (this.chartDataManager) {
          await this.chartDataManager.updateAllCharts(this.searchForm);
        }

        console.log('所有数据初始化完成');

        // 强制更新视图，确保层级隐藏逻辑正确应用
        this.$nextTick(() => {
          this.$forceUpdate();
        });

        // 数据加载完成后，确保tooltip控制器已初始化并重新启动tooltip自动滚动
        setTimeout(() => {
          console.log('数据加载完成，检查并初始化tooltip自动滚动');

          // 检查tooltip控制器是否已初始化
          if (!this.autoTooltip.deliveryTooltip || !this.autoTooltip.qualityTooltip || !this.autoTooltip.timeTooltip) {
            console.log('tooltip控制器未初始化，先进行初始化');
            this.initAutoTooltip();

            // 添加用户活动监听（如果还没有添加）
            if (!this._handleUserActivity) {
              this.setupUserActivityListeners();
            }
          }

          // 重新启动tooltip自动滚动
          this.resetAutoTooltip();
        }, 1000); // 延迟1秒，确保图表数据已完全渲染
      }, 100);
    },

    /**
     * 更新所有图表数据
     * 集中处理所有图表的数据获取，避免重复请求
     */
    async updateAllCharts() {
      if (this.chartDataManager) {
        await this.chartDataManager.updateAllCharts(this.searchForm);
      }
    },

    /**
     * 使用图表数据管理器更新质量图表
     */
    async updateQualityChart() {
      if (this.chartDataManager) {
        await this.chartDataManager.updateQualityChart(this.searchForm);
      }
    },

    /**
     * 使用图表数据管理器更新工时损失图表
     */
    async updateLostChart() {
      if (this.chartDataManager) {
        await this.chartDataManager.updateLostChart(this.searchForm);
      }
    },

    // 窗口大小变化时重新设置图表大小和按钮位置
    handleResize() {
      if (this.qualityChart) this.qualityChart.resize();
      if (this.deliveryChart) this.deliveryChart.resize();
      if (this.timeChart) this.timeChart.resize();

      // 更新回到顶部按钮位置
      this.updateBackToTopPosition();
    },
    // 处理页面滚动事件
    handleScroll() {
      // 获取内容区域元素
      const contentArea = document.querySelector('.content-area');
      if (contentArea) {
        // 当内容区域滚动超过300px时显示回到顶部按钮
        const scrollTop = contentArea.scrollTop;
        this.showBackToTop = scrollTop > 300;

        // 计算回到顶部按钮的位置
        this.updateBackToTopPosition();

        // 强制更新视图
        this.$forceUpdate();
      }
    },

    // 更新回到顶部按钮的位置
    updateBackToTopPosition() {
      this.$nextTick(() => {
        const contentArea = document.querySelector('.content-area');
        const backToTopBtn = document.querySelector('.back-to-top');

        if (contentArea && backToTopBtn) {
          // 获取内容区域的位置和尺寸
          const contentRect = contentArea.getBoundingClientRect();

          // 计算按钮应该在的位置
          // 按钮应该在内容区域的右下角，距离右边和底部各20px
          const rightPosition = window.innerWidth - contentRect.right + 20;

          // 设置按钮的位置
          backToTopBtn.style.right = `${rightPosition}px`;
          backToTopBtn.style.bottom = '120px';
        }
      });
    },
    // 回到页面顶部
    scrollToTop() {
      // 获取内容区域元素
      const contentArea = document.querySelector('.content-area');
      if (contentArea) {
        // 平滑滚动到顶部
        contentArea.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    },
    setActiveQualityBtn(type) {
      // 只改变状态值，不直接调用更新方法
      // watch监听器会自动处理图表更新
      this.activeQualityBtn = type;
    },
    setActiveLostBtn(type) {
      // 只改变状态值，不直接调用更新方法
      // watch监听器会自动处理图表更新
      this.activeLostBtn = type;
    },



    // 处理表格变化（分页、筛选、排序）
    handleTableChange(pagination, filters) {
      console.log('表格变化事件:', { pagination, filters });

      this.tableFilters = filters
      // 更新分页信息
      this.pagination.current = pagination.current;
      this.pagination.pageSize = pagination.pageSize;

      // 重新请求数据
      // 检查filters和currentState是否存在，避免undefined错误
      if (filters && filters.eventStatus && filters.eventStatus.length) {
        // 只有两种状态不传递数组
        if (filters.eventStatus.length === 2) {
          // 两个都选等于都没选
          this.fetchTableData();
          return;
        }
        const eventStatusValue = filters.eventStatus.map(item => Number(item))[0]
        console.log(eventStatusValue)
        this.fetchTableData({ eventStatus: eventStatusValue });
      } else {
        this.fetchTableData()
      }
    },
    // 获取表格数据的方法
    async fetchTableData(params) {
      // 在初始化期间不执行表格数据获取，避免触发不必要的API调用
      if (this.isInitializingOrg) {
        console.log('组织层级初始化中，跳过表格数据获取');
        return;
      }

      // 设置加载状态
      this.tableLoading = true;

      try {
        const payload = {
          "pageNum": this.pagination.current,
          "pageSize": this.pagination.pageSize,
          "problemSource": "8",
          "happenTime": this.searchForm.date,

          "workshopName": this.searchForm.workshopName,
          "teamName": this.searchForm.teamName,
          "factoryName": this.searchForm.factoryName,
          "companyName": this.searchForm.baseName,
          "branchFactoryName": this.searchForm.branchName,
          ...params
        };

        console.log('发送表格数据请求:', {
          pageNum: payload.pageNum,
          pageSize: payload.pageSize,
          params: params,
          payload: payload
        });

        // 调用获取表格数据的方法
        const result = await getTableData(payload);

        // 更新表格数据和分页信息
        this.tableData = result;

        // 更新事件统计信息（从第一项获取统计数据）
        if (result && result.data && Array.isArray(result.data) && result.data.length > 0) {
          const row = result.data[0];
          console.log(row, 'row');
          this.eventIssues.totalCount = row.count || 0;
          this.eventIssues.completedCount = row.completeCount || 0;
          this.eventIssues.unCompleteCount = row.incompleteCount || 0;
        } else {
          // 如果没有数据，重置统计信息
          console.log('表格数据为空，重置事件统计信息');
          this.eventIssues.totalCount = 0;
          this.eventIssues.completedCount = 0;
          this.eventIssues.unCompleteCount = 0;
        }

        // 更新分页总数
        if (result && typeof result === 'object') {
          this.pagination.total = result.total || (result.data && result.data.total) || 0;
        } else {
          this.pagination.total = 0;
        }
      } catch (error) {
        console.error("获取表格数据失败:", error);
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        // 无论成功还是失败，都关闭加载状态
        this.tableLoading = false;
      }
    },
    // 获取所有字典值
    async fetchAllDictsData() {
      try {
        this.allDictsData = await getAllDictsData();
      } catch (error) {
        console.error("获取字典数据失败:", error);
      }
    },


    async initTableData() {
      // 重置分页到第一页
      this.pagination.current = 1;
      // 调用fetchTableData方法获取数据
      await this.fetchTableData();
    },
    initCharts() {
      // 初始化所有图表
      this.initQualityChart();
      this.initDeliveryChart();
      this.initTimeChart();

      // 注意：tooltip自动滚动的初始化已经移到mounted方法中
      // 这里不再初始化tooltip自动滚动，避免重复初始化
    },
    initQualityChart() {
      if (!this.$refs.qualityChart) {
        return;
      }

      try {
        this.qualityChart = initQualityChart(
          this.$refs.qualityChart,
          this.darkTheme
        );

        // 确保图表实例有tooltip配置
        if (this.qualityChart && !this.qualityChart.getOption().tooltip) {
          this.qualityChart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            }
          });
        }

        // 添加restore事件监听器
        this.qualityChart.off('restore');
        this.qualityChart.on('restore', () => {
          // 在还原后重新获取数据
          setTimeout(() => {
            this.updateQualityChart();
          }, 100);
        });
      } catch (error) {
        // 初始化失败时不做特殊处理
      }
    },
    initDeliveryChart() {
      if (!this.$refs.deliveryChart) {
        return;
      }

      try {
        // 添加点击事件回调函数
        const handleSeriesClick = (params) => {
          console.log(params, '====')
          // 获取点击的数据索引
          const dataIndex = params.dataIndex;

          // 获取图表中存储的原始数据
          const rawData = this.deliveryChart._rawData;

          if (rawData && rawData.details && Array.isArray(rawData.details) && rawData.details.length > 0) {
            // 直接使用已排序的详情数据，不再进行排序
            const details = rawData.details;

            // 获取点击的详情数据
            const clickedData = details[dataIndex];

            if (clickedData) {
              // 显示详情对话框
              this.$refs.detailDataDialog.show(clickedData);
            }
          }
        };

        this.deliveryChart = initDeliveryChart(
          this.$refs.deliveryChart,
          this.darkTheme,
          handleSeriesClick // 传递点击事件回调函数
        );

        // 确保图表实例有tooltip配置
        if (this.deliveryChart && !this.deliveryChart.getOption().tooltip) {
          this.deliveryChart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            }
          });
        }

        // 添加restore事件监听器
        this.deliveryChart.off('restore');
        this.deliveryChart.on('restore', () => {
          // 在还原后重新获取数据
          setTimeout(() => {
            this.fetchDailyProductPlanRate();
          }, 100);
        });
      } catch (error) {
        // 初始化失败时不做特殊处理
      }
    },
    initTimeChart() {
      if (!this.$refs.timeChart) {
        return;
      }

      try {
        this.timeChart = initTimeChart(this.$refs.timeChart, this.darkTheme);

        // 确保图表实例有tooltip配置
        if (this.timeChart && !this.timeChart.getOption().tooltip) {
          this.timeChart.setOption({
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow'
              }
            }
          });
        }

        // 添加restore事件监听器
        this.timeChart.off('restore');
        this.timeChart.on('restore', () => {
          // 在还原后重新获取数据
          setTimeout(() => {
            this.updateLostChart();
          }, 100);
        });
      } catch (error) {
        // 初始化失败时不做特殊处理
      }
    },
    disposeCharts() {
      // 销毁所有图表实例
      if (this.qualityChart) this.qualityChart.dispose();
      if (this.deliveryChart) this.deliveryChart.dispose();
      if (this.timeChart) this.timeChart.dispose();
    },
    // 在ai块中点击采纳的事件
    pushToSummarize(msg) {
      this.aiSummarize.push(msg);
    },
    // 渲染markdown内容
    renderMarkdown(content) {
      if (!content) return '';
      try {
        // 使用marked库渲染markdown内容
        return marked(content, {
          breaks: true, // 允许换行
          gfm: true // 启用GitHub风格的markdown
        });
      } catch (error) {
        return content; // 如果渲染失败，返回原始内容
      }
    },
    // 打开模态框
    handleOpenDialog() {
      this.isDialogVisible = true;
    },
    // 隐藏所有图表的tooltip
    hideAllTooltips() {
      // 关闭所有图表的tooltip
      if (this.deliveryChart) {
        this.deliveryChart.dispatchAction({
          type: 'hideTip'
        });
      }

      if (this.qualityChart) {
        this.qualityChart.dispatchAction({
          type: 'hideTip'
        });
      }

      if (this.timeChart) {
        this.timeChart.dispatchAction({
          type: 'hideTip'
        });
      }

      // 如果启用了tooltip自动滚动，暂停它
      if (this.autoTooltip && this.autoTooltip.enabled) {
        stopAutoTooltip(this.autoTooltip);
      }
    },

    // 取消模态框
    handleDialogCancel() {
      this.isDialogVisible = false;
    },
    // 表格升级操作
    async tableHandleUpgrade(record) {
      try {
        // 处理表格升级操作
        const result = await queryWorkOrderDataUpgrade(record.eventId);
        if (result) {
          this.$message.success('升级成功');
          // 升级成功后重新获取表格数据
          // 检查tableFilters和currentState是否存在，避免undefined错误
          if (this.tableFilters && this.tableFilters.currentState && this.tableFilters.currentState.length) {
            if (this.tableFilters.currentState.length === 2) {
              this.fetchTableData();
              return;
            }
            this.fetchTableData({ currentStates: this.tableFilters.currentState.map(item => Number(item))[0] });
          } else {
            this.fetchTableData()
          }
        }
      } catch (error) {
        this.$message.error('升级失败');
        console.error('升级失败:', error);
      }
    },
    // 主题切换
    handleThemeChange(checked) {
      this.darkTheme = checked;

      // 为使用组件添加暗色主题标记
      if (checked) {
        document.body.classList.add("has-dark-theme");
      } else {
        document.body.classList.remove("has-dark-theme");

        // 移除可能存在的时间选择器面板
        const timePickerPanels = document.querySelectorAll('.ant-calendar-time-picker-panel');
        if (timePickerPanels.length > 0) {
          timePickerPanels.forEach(panel => {
            panel.parentNode.removeChild(panel);
          });
        }
      }

      // 停止当前的tooltip自动滚动
      stopAutoTooltip(this.autoTooltip);

      // 清理tooltip自动滚动相关资源
      this.cleanupAutoTooltip();

      // 保存当前图表数据
      const saveChartData = () => {
        const savedData = {};

        // 保存交付图表数据
        if (this.deliveryChart) {
          try {
            const option = this.deliveryChart.getOption();
            savedData.delivery = {
              xAxis: option.xAxis[0].data,
              series: option.series.map(series => series.data)
            };
          } catch (error) {
            // 忽略错误
          }
        }

        // 保存质量图表数据
        if (this.qualityChart) {
          try {
            const option = this.qualityChart.getOption();
            savedData.quality = {
              xAxis: option.xAxis[0].data,
              series: option.series.map(series => series.data)
            };
          } catch (error) {
            // 忽略错误
          }
        }

        // 保存工时损失图表数据
        if (this.timeChart) {
          try {
            const option = this.timeChart.getOption();
            savedData.time = {
              xAxis: option.xAxis[0].data,
              series: option.series.map(series => series.data)
            };
          } catch (error) {
            // 忽略错误
          }
        }

        return savedData;
      };

      // 保存当前图表数据
      const savedChartData = saveChartData();

      // 重新初始化图表以应用主题样式，并恢复之前的数据
      this.$nextTick(() => {
        // 初始化图表
        this.initQualityChart();
        this.initDeliveryChart();
        this.initTimeChart();

        // 恢复保存的数据
        const restoreChartData = () => {
          // 恢复交付图表数据
          if (this.deliveryChart && savedChartData.delivery) {
            try {
              const option = this.deliveryChart.getOption();
              option.xAxis[0].data = savedChartData.delivery.xAxis;

              // 恢复系列数据
              savedChartData.delivery.series.forEach((data, index) => {
                if (option.series[index]) {
                  option.series[index].data = data;
                }
              });

              this.deliveryChart.setOption(option);
            } catch (error) {
              // 如果恢复失败，重新获取数据
              this.fetchDailyProductPlanRate();
            }
          } else if (this.deliveryChart) {
            // 如果没有保存的数据，重新获取
            this.fetchDailyProductPlanRate();
          }

          // 恢复质量图表数据
          if (this.qualityChart && savedChartData.quality) {
            try {
              const option = this.qualityChart.getOption();
              option.xAxis[0].data = savedChartData.quality.xAxis;

              // 恢复系列数据
              savedChartData.quality.series.forEach((data, index) => {
                if (option.series[index]) {
                  option.series[index].data = data;
                }
              });

              this.qualityChart.setOption(option);
            } catch (error) {
              // 如果恢复失败，重新获取数据
              this.updateQualityChart();
            }
          } else if (this.qualityChart) {
            // 如果没有保存的数据，重新获取
            this.updateQualityChart();
          }

          // 恢复工时损失图表数据
          if (this.timeChart && savedChartData.time) {
            try {
              const option = this.timeChart.getOption();
              option.xAxis[0].data = savedChartData.time.xAxis;

              // 恢复系列数据
              savedChartData.time.series.forEach((data, index) => {
                if (option.series[index]) {
                  option.series[index].data = data;
                }
              });

              this.timeChart.setOption(option);
            } catch (error) {
              // 如果恢复失败，重新获取数据
              this.updateLostChart();
            }
          } else if (this.timeChart) {
            // 如果没有保存的数据，重新获取
            this.updateLostChart();
          }
        };

        // 恢复图表数据
        restoreChartData();

        // 重新初始化tooltip自动滚动
        setTimeout(() => {
          this.initAutoTooltip();

          // 如果启用了自动滚动，则启动
          if (this.autoTooltip.enabled) {
            this.startAutoTooltip();
          }
        }, 1000);
      });
    },
    // 根据状态码获取状态文本
    getStatusText(statusCode) {
      // 状态码映射：待接单(4)、进行中(10)、已完成(12)、已超时(99)
      const statusMap = {
        '4': '待接单',
        '10': '进行中',
        '12': '已完成',
        '99': '已超时'
      };

      return statusMap[statusCode] || '未知状态';
    },

    // 根据事件来源码获取事件来源文本
    getEventSourceText(sourceCode) {
      // 事件来源码映射：1：安灯事件源，2指标事件来源，3质量巡检来源，4spc来源，5物料预警来源，6云图事件来源，7工艺事件来源，8手创工单来源，9点检工单来源
      const sourceMap = {
        '1': '安灯事件',
        '2': '指标事件',
        '3': '质量巡检',
        '4': 'SPC事件',
        '5': '物料预警',
        '6': '云图事件',
        '7': '工艺事件',
        '8': '手创工单',
        '9': '点检工单'
      };

      return sourceMap[sourceCode] || '未知来源';
    },

    getCategoryClass(statusCode) {
      // 根据状态码返回相应的类名
      switch (statusCode) {
        case '4':
          return "status-pending";
        case '10':
          return "status-processing";
        case '12':
          return "status-completed";
        case '99':
          return "status-timeout";
        default:
          return "";
      }
    },
    // 责任人搜索过滤方法
    filterResponsiblePerson(input, option) {
      // 将输入和选项文本都转为小写进行比较，实现不区分大小写的搜索
      const inputLower = input.toLowerCase();
      const textLower = option.componentOptions.children[0].text.toLowerCase();
      return textLower.indexOf(inputLower) >= 0;
    },

    // ===== tooltip自动滚动相关方法 =====

    // 初始化tooltip自动滚动
    initAutoTooltip() {
      console.log('开始初始化tooltip自动滚动');

      // 确保图表实例存在
      if (!this.deliveryChart || !this.qualityChart || !this.timeChart) {
        console.warn('图表实例不存在，无法初始化tooltip:', {
          hasDeliveryChart: !!this.deliveryChart,
          hasQualityChart: !!this.qualityChart,
          hasTimeChart: !!this.timeChart
        });
        return;
      }

      // 使用抽离的方法初始化tooltip自动滚动
      const charts = {
        deliveryChart: this.deliveryChart,
        qualityChart: this.qualityChart,
        timeChart: this.timeChart
      };

      // 创建tooltip控制器
      const tooltipControllers = initAutoTooltip(charts, {
        interval: 2000, // 2秒切换一次
        loop: true,
        debug: false // 禁用调试模式
      });

      if (tooltipControllers) {
        // 保存tooltip控制器
        this.autoTooltip.deliveryTooltip = tooltipControllers.deliveryTooltip;
        this.autoTooltip.qualityTooltip = tooltipControllers.qualityTooltip;
        this.autoTooltip.timeTooltip = tooltipControllers.timeTooltip;

        console.log('tooltip控制器初始化成功:', {
          hasDeliveryTooltip: !!this.autoTooltip.deliveryTooltip,
          hasQualityTooltip: !!this.autoTooltip.qualityTooltip,
          hasTimeTooltip: !!this.autoTooltip.timeTooltip
        });

        // 为每个图表添加鼠标事件监听
        this.setupChartMouseEvents();
      } else {
        console.error('tooltip控制器初始化失败');
      }
    },

    // 设置图表鼠标事件监听
    setupChartMouseEvents() {
      // 使用抽离的方法设置图表鼠标事件监听
      setupChartMouseEvents(this.$refs, this.autoTooltip);
    },

    // 设置用户活动监听
    setupUserActivityListeners() {
      // 用户活动处理函数
      const handleUserActivity = () => {
        // 标记用户处于活动状态
        this.autoTooltip.isUserActive = true;

        // 如果自动滚动已启用，则暂停所有图表的tooltip自动滚动
        if (this.autoTooltip.enabled) {
          pauseAutoTooltip(this.autoTooltip);
        }

        // 清除之前的定时器
        if (this.autoTooltip.inactivityTimer) {
          clearTimeout(this.autoTooltip.inactivityTimer);
        }

        // 设置新的定时器，在指定时间后恢复自动滚动
        this.autoTooltip.inactivityTimer = setTimeout(() => {
          // 标记用户不再活动
          this.autoTooltip.isUserActive = false;

          // 如果自动滚动已启用，则重新启动所有图表的tooltip自动滚动
          // 使用重新启动而不是恢复，确保能正确处理数据变化
          if (this.autoTooltip.enabled) {
            // 先停止所有自动滚动
            stopAutoTooltip(this.autoTooltip);

            // 延迟一点时间后重新启动
            setTimeout(() => {
              console.log('用户无操作超时，重新启动自动滚动');
              this.startAutoTooltip();
            }, 500);
          }
        }, this.autoTooltip.inactivityTimeout);
      };

      // 使用抽离的方法设置用户活动监听
      const activityListeners = setupUserActivityListeners(handleUserActivity);

      // 保存事件处理函数引用，以便后续移除
      this._handleUserActivity = activityListeners.handleUserActivity;
      this._userActivityEvents = activityListeners.userActivityEvents;
    },

    // 移除用户活动监听
    removeUserActivityListeners() {
      // 使用抽离的方法移除用户活动监听
      const activityListeners = {
        handleUserActivity: this._handleUserActivity,
        userActivityEvents: this._userActivityEvents
      };

      removeUserActivityListeners(activityListeners);

      // 清除定时器
      if (this.autoTooltip.inactivityTimer) {
        clearTimeout(this.autoTooltip.inactivityTimer);
        this.autoTooltip.inactivityTimer = null;
      }
    },

    // 启动所有图表的tooltip自动滚动
    startAutoTooltip() {
      // 强制将用户活动状态设置为false，确保可以启动自动滚动
      this.autoTooltip.isUserActive = false;

      console.log('启动tooltip自动滚动，当前状态:', {
        enabled: this.autoTooltip.enabled,
        isUserActive: this.autoTooltip.isUserActive,
        hasDeliveryChart: !!this.deliveryChart,
        hasQualityChart: !!this.qualityChart,
        hasTimeChart: !!this.timeChart,
        hasDeliveryTooltip: !!this.autoTooltip.deliveryTooltip,
        hasQualityTooltip: !!this.autoTooltip.qualityTooltip,
        hasTimeTooltip: !!this.autoTooltip.timeTooltip
      });

      // 使用抽离的方法启动所有图表的tooltip自动滚动
      const charts = {
        deliveryChart: this.deliveryChart,
        qualityChart: this.qualityChart,
        timeChart: this.timeChart
      };

      startAutoTooltip(this.autoTooltip, charts, checkChartHasData);
    },

    // 重置所有图表的tooltip自动滚动
    resetAutoTooltip() {
      console.log('重置tooltip自动滚动，当前状态:', {
        enabled: this.autoTooltip.enabled,
        isUserActive: this.autoTooltip.isUserActive,
        hasDeliveryChart: !!this.deliveryChart,
        hasQualityChart: !!this.qualityChart,
        hasTimeChart: !!this.timeChart,
        hasDeliveryTooltip: !!this.autoTooltip.deliveryTooltip,
        hasQualityTooltip: !!this.autoTooltip.qualityTooltip,
        hasTimeTooltip: !!this.autoTooltip.timeTooltip
      });

      // 清除可能存在的定时器
      if (this.autoTooltip.inactivityTimer) {
        clearTimeout(this.autoTooltip.inactivityTimer);
        this.autoTooltip.inactivityTimer = null;
      }

      // 强制将用户活动状态设置为false，确保可以重新启动自动滚动
      this.autoTooltip.isUserActive = false;

      // 使用抽离的方法重置所有图表的tooltip自动滚动
      const charts = {
        deliveryChart: this.deliveryChart,
        qualityChart: this.qualityChart,
        timeChart: this.timeChart
      };

      resetAutoTooltip(this.autoTooltip, charts, checkChartHasData, this.autoTooltip.enabled);
    },

    // 重置交付图表的tooltip自动滚动
    resetDeliveryTooltip() {
      // 强制将用户活动状态设置为false，确保可以重新启动自动滚动
      this.autoTooltip.isUserActive = false;

      // 使用抽离的方法重置交付图表的tooltip自动滚动
      resetChartTooltip(
        this.autoTooltip.deliveryTooltip,
        this.deliveryChart,
        checkChartHasData,
        this.autoTooltip.enabled
      );
    },

    // 重置质量图表的tooltip自动滚动
    resetQualityTooltip() {
      // 强制将用户活动状态设置为false，确保可以重新启动自动滚动
      this.autoTooltip.isUserActive = false;

      // 使用抽离的方法重置质量图表的tooltip自动滚动
      resetChartTooltip(
        this.autoTooltip.qualityTooltip,
        this.qualityChart,
        checkChartHasData,
        this.autoTooltip.enabled
      );
    },

    // 重置工时损失图表的tooltip自动滚动
    resetTimeTooltip() {
      // 强制将用户活动状态设置为false，确保可以重新启动自动滚动
      this.autoTooltip.isUserActive = false;

      // 使用抽离的方法重置工时损失图表的tooltip自动滚动
      resetChartTooltip(
        this.autoTooltip.timeTooltip,
        this.timeChart,
        checkChartHasData,
        this.autoTooltip.enabled
      );
    },

    // 清理tooltip自动滚动相关资源
    cleanupAutoTooltip() {
      // 停止所有图表的tooltip自动滚动
      stopAutoTooltip(this.autoTooltip);

      // 清除定时器
      if (this.autoTooltip.inactivityTimer) {
        clearTimeout(this.autoTooltip.inactivityTimer);
        this.autoTooltip.inactivityTimer = null;
      }

      // 清理图表鼠标事件监听
      cleanupChartMouseEvents(this.$refs);

      // 清空控制器引用
      this.autoTooltip.deliveryTooltip = null;
      this.autoTooltip.qualityTooltip = null;
      this.autoTooltip.timeTooltip = null;
    },

    /**
     * 测试请求参数逻辑
     */
    async handleGETData() {
      console.log('当前searchForm:', this.searchForm);
      console.log('localStorage中的plantCode:', window.localStorage.getItem("plantCode") || "6210");
    },

    /**
     * 启动图表自动刷新定时器
     * 每5分钟自动刷新一次所有图表数据
     */
    startChartRefreshTimer() {
      try {
        // 如果已经有定时器在运行，先清除它
        this.clearChartRefreshTimer();

        // 如果自动刷新被禁用，不启动定时器
        if (!this.isChartRefreshEnabled) {
          console.log('图表自动刷新已禁用，不启动定时器');
          return;
        }

        console.log(`启动图表自动刷新定时器，间隔: ${this.chartRefreshInterval / 1000 / 60} 分钟`);

        // 设置定时器
        this.chartRefreshTimer = setInterval(() => {
          console.log('定时器触发：开始自动刷新图表数据');

          // 检查是否处于初始化状态，如果是则跳过刷新
          if (this.isInitializingOrg) {
            console.log('组织层级初始化中，跳过自动刷新');
            return;
          }

          // 执行图表数据刷新
          this.updateAllCharts().catch(error => {
            console.error('自动刷新图表数据失败:', error);
          });
        }, this.chartRefreshInterval);

        console.log('图表自动刷新定时器启动成功');
      } catch (error) {
        console.error('启动图表自动刷新定时器失败:', error);
      }
    },

    /**
     * 清除图表自动刷新定时器
     * 避免内存泄漏
     */
    clearChartRefreshTimer() {
      try {
        if (this.chartRefreshTimer) {
          console.log('清除图表自动刷新定时器');
          clearInterval(this.chartRefreshTimer);
          this.chartRefreshTimer = null;
        }
      } catch (error) {
        console.error('清除图表自动刷新定时器失败:', error);
      }
    },

    /**
     * 重置图表自动刷新定时器
     * 在图表切换时调用，重新开始计时
     */
    resetChartRefreshTimer() {
      try {
        console.log('重置图表自动刷新定时器');

        // 清除现有定时器
        this.clearChartRefreshTimer();

        // 重新启动定时器
        this.startChartRefreshTimer();
      } catch (error) {
        console.error('重置图表自动刷新定时器失败:', error);
      }
    },

    /**
     * 启用/禁用图表自动刷新
     * @param {boolean} enabled - 是否启用自动刷新
     */
    setChartRefreshEnabled(enabled) {
      try {
        this.isChartRefreshEnabled = enabled;

        if (enabled) {
          console.log('启用图表自动刷新');
          this.startChartRefreshTimer();
        } else {
          console.log('禁用图表自动刷新');
          this.clearChartRefreshTimer();
        }
      } catch (error) {
        console.error('设置图表自动刷新状态失败:', error);
      }
    },

    /**
     * 设置图表自动刷新间隔
     * @param {number} interval - 刷新间隔（毫秒）
     */
    setChartRefreshInterval(interval) {
      try {
        if (interval < 60000) { // 最小间隔1分钟
          console.warn('刷新间隔不能小于1分钟，已设置为1分钟');
          interval = 60000;
        }

        this.chartRefreshInterval = interval;
        console.log(`设置图表自动刷新间隔为: ${interval / 1000 / 60} 分钟`);

        // 如果定时器正在运行，重置它以应用新的间隔
        if (this.chartRefreshTimer) {
          this.resetChartRefreshTimer();
        }
      } catch (error) {
        console.error('设置图表自动刷新间隔失败:', error);
      }
    }
  },
};
</script>

<style lang="less" scoped>
/* 普通DOM节点样式 */
.workshop-meeting {
  width: 100%;
  background-color: #ebf4f4;
  color: #333;
  height: calc(100vh - 115px);
  overflow-y: auto;
  transition: all 0.5s ease;

  &.dark-theme {
    background-color: #01141e !important;
    color: #fff !important;
  }
}

/* 修改主容器布局样式 */
.main-container {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  /* 防止出现横向滚动条 */
}

.content-area {
  width: 100%;
  min-width: 0;
  transition: all 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  /* 添加相对定位，使其成为定位上下文 */
  /* 防止横向滚动条 */

  &-with-sidebar {
    width: calc(100% - 400px);
  }
}

.ai-sidebar {
  position: absolute;
  right: 0;
  top: 0;
  width: 400px;
  /* 固定宽度为25% */
  height: 100%;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  transform: translateX(100%);
  transition: transform 0.3s ease;

  &.visible {
    transform: translateX(0);
  }

  .dark-theme & {
    background: #08343c;
    border-left-color: #00aaa6;
  }

  .ai-header {
    height: 56px;
    padding: 0 16px;
    border-bottom: 1px solid #e8e8e8;
    background: linear-gradient(270deg,
        rgba(0, 170, 166, 0) -22%,
        rgba(0, 170, 166, 0.6) 87%);

    .dark-theme & {
      border-bottom-color: #00aaa6;
    }

    .ai-header-left {
      height: 100%;
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .ai-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
    }

    .ai-title {
      font-size: 18px;
      font-weight: 500;
      color: #1d2129;
      font-weight: bold;

      .dark-theme & {
        color: #fff;
      }
    }

    .ai-header-right {
      margin-left: auto;

      .close-icon {
        font-size: 16px;
        cursor: pointer;
        color: #1d2129;
        transition: color 0.3s ease;

        .dark-theme & {
          color: #fff;
        }

        &:hover {
          color: #00aaa6;
        }
      }
    }
  }
}

.header {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  padding: 0;
  /* 重置内边距 */

  .title {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    height: 72.54px;
    background-image: url("~@/assets/images/workShop_standing_meeting/head_banner.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 4px;
    margin-bottom: 12px;
  }

  .filter-container {
    width: 100%;
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    padding: 0;
    flex-wrap: wrap;
    /* 重置内边距 */

    .filter-wrapper {
      border-radius: 4px;
      flex: 1;

      &.with-bg {
        background-color: #fff;
      }
    }

    .button-group {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 8px;
      border-radius: 4px;

      &.with-bg {
        background-color: #fff;
      }
    }

    .ai_btn {
      width: 120px;
      height: 32px;
      float: none;
      margin-right: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;

      .ai-icon {
        width: 20px;
        height: 20px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }

    .dark-theme .ai_btn {
      background: linear-gradient(0deg, #00afad 0%, #00e5a4 100%);
      border: 1px solid #00aaa6;
      color: #fff;
    }

    .QRCode_btn {
      // width: 100px;
      height: 32px;
      font-size: 16px;
      display: flex;
      align-items: center;
      font-weight: 500;
      color: #1d2129;

      .ai-icon {
        width: 16px;
        height: 16px;
        margin-right: 5px;
        vertical-align: middle;
      }
    }

    .dark-theme .QRCode_btn {
      border: 1px solid #00aaa6;
      background-color: #08343c !important;
    }

    .moreOperator_btn {
      width: 80px;
      height: 32px;
      color: #1d2129;
      font-size: 16px;
      font-weight: 500;

      .moreOperator_icon {
        color: #1d2129;
      }
    }

    .dark-theme .moreOperator_btn {
      border: 1px solid #00aaa6;
      background-color: #08343c !important;
      color: #ffffff;
    }

    .filter-items {
      display: flex;
      height: 100%;
      flex-wrap: wrap;
      align-items: baseline;
      padding-top: 8px;
    }

    .filter-item-container {
      padding-right: 8px;
      margin-bottom: 8px;
      // width: 180px;
    }

    .filter-item {
      width: 100%;
      border-radius: 4px;
    }
  }
}

.main-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1840px;
  margin: 0 auto;
  padding: 0;
  overflow-x: hidden;
  /* 防止横向滚动条 */
  /* 重置内边距 */
}

.top-charts {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.bottom-charts {
  display: flex;
  flex-wrap: wrap;
  /* 允许在空间不足时换行 */
  gap: 12px;
  /* 使用gap替代margin，更好的响应式布局 */

  .card-container {
    flex: 1;
    min-width: 300px;
    /* 设置最小宽度，防止过度挤压 */
  }
}

.card-container {
  width: 100%;
  min-width: 0;
  /* 允许卡片收缩 */
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  background: #fff;
  padding: 0;
  transition: background-color 0.5s ease, color 0.5s ease;

  &.full-card {
    width: 100%;
  }

  .card-title {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background: linear-gradient(90deg, #00aaa6 15%, rgba(78, 224, 220, 0) 99%);
    position: relative;
    align-items: center;

    .title-icon {
      width: 15px;
      height: 15px;
      background: url("~@/assets/images/workShop_standing_meeting/cardTitleIcon.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
      border-radius: 2px;
      z-index: 1;
    }

    .card-title-text {
      font-size: 22px;
      font-weight: bold;
      line-height: 25px;
      letter-spacing: normal;
      color: #ffffff;
    }

    .quality-tab-btns,
    .lost-tab-btns {
      display: flex;
      margin-left: auto;

      .operatorBtnItem {
        height: 28px;
        margin-right: 10px;
        border-radius: 0;

        &:last-child {
          margin-right: 0;
        }

        div {
          padding: 0 8px;
          line-height: 28px;
        }
      }
    }

    .addExceptionBtn {
      height: 32px;
      background: #00aaa6;
      color: #fff;
      border: none;
      margin-left: auto;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .card-content {
    padding: 16px;

    .overview {
      display: flex;
      margin-bottom: 20px;
      padding-left: 10px;

      .quality-item {
        min-width: 84px;
        height: 47px;
        text-align: center;
        margin-right: 40px;

        .item-value {
          font-size: 20px;
          font-weight: bold;
          color: #1d2129;
        }

        .item-label {
          font-size: 12px;
          color: #666;
          margin-top: 4px;
        }
      }
    }

    .overview:last-child {
      padding-left: 100px;
    }

    .issue-overview {
      display: flex;
      height: 60px;
      margin-bottom: 10px;
      margin-left: 10px;

      .issue-item {
        min-width: 100px;
        height: 47px;
        text-align: center;
        margin-right: 40px;

        .item-value {
          font-size: 20px;
          font-weight: bold;
          color: #1d2129;
        }

        .item-label {
          // font-size: 14px;
          color: #666;
          margin-top: 4px;
        }
      }
    }

    .chart-container {
      position: relative;

      .chart {
        height: 300px;
        width: 100%;
        background-color: #fff;
        transition: all 0.3s ease; // 添加过渡效果使图表重绘更平滑
        z-index: 0;
      }
    }
  }
}

.table-container {
  width: 100%;
  max-width: 1840px;
  margin: 20px auto 24px auto;
  padding: 0;
  /* 重置内边距 */
  overflow-x: hidden;
  /* 防止容器本身产生横向滚动条 */

  .ant-table-wrapper {
    width: 100%;
    overflow-x: auto;
    padding: 0;
    /* 重置表格容器的内边距 */
  }

  .ant-table {
    /* 移除最小宽度限制，让表格自适应容器宽度 */
    width: 100%;

    .ant-table-body {
      overflow-x: auto;
    }
  }
}

.noRadiusBtn {
  border-radius: 0 !important;
}

.modal-title {
  padding-left: 20px;
  height: 56px;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(270deg,
      rgba(0, 170, 166, 0) -22%,
      rgba(0, 170, 166, 0.6) 87%);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_head_banner.png");
    z-index: 0;
  }

  .title-icon {
    width: 15px;
    height: 15px;
    background: url("~@/assets/images/workShop_standing_meeting/dialog_banner_title_icon.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-right: 8px;
    position: relative;
    z-index: 1;
  }

  .title-text {
    font-size: 20px;
    font-weight: bold;
    line-height: normal;
    letter-spacing: normal;
    color: #1d2129;
    position: relative;
    z-index: 1;
  }
}

// 模态框样式
.modal-content {
  padding: 10px;

  .form-row {
    display: flex;
    justify-content: space-between;

    .form-item {
      width: 48%;
    }

    .form-width {
      width: 100%;
    }
  }

  .form-item {
    margin-bottom: 15px;

    .form-label {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .form-input {
      width: 100%;
      border-radius: 0;

      &:hover,
      &:focus {
        border-color: #00aaa6;
      }
    }
  }

  .modal-footer {
    margin-top: 20px;
    text-align: right;

    .cancel-btn {
      border-radius: 0;
    }

    .confirm-btn {
      margin-right: 10px;
      background-color: #00aaa6;
      border-color: #00aaa6;
      border-radius: 0;
      color: #fff;

      &:hover {
        background-color: #33c3bf;
        border-color: #33c3bf;
      }
    }
  }
}

.user-icon {
  width: 16px;
  height: 16px;
}

.popover-item {
  width: 154px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 10px;
  margin: 10px auto;
  font-size: 16px;
  // color: #4e5969;
  cursor: pointer;
  border-bottom: 1px solid #f2f3f5;
  border-radius: 12px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    // background: #f2f3f5;
    color: #00aaa6;
  }

  div {
    font-weight: 400;
  }

  i {
    color: #000000;
  }
}

/* 自定义状态标签样式 */
.category-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 24px;
  border: 1px solid transparent;
  border-radius: 2px;
  padding: 0 8px;

  /* 待接单状态 - 橙色 */
  &.status-pending {
    border-color: #ff9800;
    background-color: rgba(255, 152, 0, 0.1);

    .category-text {
      color: #ff9800;
    }
  }

  /* 进行中状态 - 绿色 */
  &.status-processing {
    border-color: #52c41a;
    background-color: rgba(82, 196, 26, 0.1);

    .category-text {
      color: #52c41a;
    }
  }

  /* 已完成状态 - 蓝色 */
  &.status-completed {
    border-color: #1890ff;
    background-color: rgba(24, 144, 255, 0.1);

    .category-text {
      color: #1890ff;
    }
  }

  /* 已超时状态 - 红色 */
  &.status-timeout {
    border-color: #f5222d;
    background-color: rgba(245, 34, 45, 0.1);

    .category-text {
      color: #f5222d;
    }
  }

  .category-text {
    font-size: 12px;
    font-weight: 500;
  }
}
</style>

<style lang="less">
/* Ant Design组件样式覆盖 */

/* 表格样式覆盖 */
.ant-table {
  background-color: #fff;
  color: #333;
  transition: background-color 0.5s ease, color 0.5s ease;

  .ant-table-thead {
    background-color: #ddf3f3;
  }

  .ant-table-thead>tr>th {
    background-color: #f5f5f5;
    color: #333;
    font-weight: bold;
    border-bottom: 1px solid #e8e8e8;
    padding: 10px 8px;
    transition: background-color 0.5s ease, color 0.5s ease,
      border-color 0.5s ease;
  }

  .ant-table-body {
    margin: 0 !important;
  }

  .ant-table-tbody>tr>td {
    border-bottom: 1px solid #e8e8e8;
    padding: 8px;
    transition: border-color 0.5s ease, color 0.5s ease;
  }

  .table-row {
    background-color: #fff;
    transition: background-color 0.5s ease;

    &:nth-child(even) {
      background-color: #f9f9f9;
    }

    &:hover {
      background-color: rgba(0, 181, 177, 0.1) !important;
    }
  }
}

/* 组织选择下拉框样式 */
.org-selects-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.org-selects-row {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-items: center;
  width: 100%;
  overflow-x: auto;
  padding: 4px 0;
}

/* 下拉选择框样式覆盖 */
.ant-select {
  color: #333;
  transition: color 0.5s ease;

  .ant-select-selection {
    background-color: #fff;
    border: 1px solid #00aaa6;
    border-radius: 4px;
    transition: background-color 0.5s ease;

    &:hover {
      border-color: #00b5b1;
    }
  }
}

/* 输入框样式覆盖 */
.ant-input,
.ant-calendar-picker-input {
  background-color: #fff;
  border: 1px solid #00aaa6;
  color: #333;
  border-radius: 4px;
  transition: background-color 0.5s ease, color 0.5s ease;

  &:hover,
  &:focus {
    border-color: #00b5b1;
  }
}

/* 下拉箭头和日历图标样式 */
.ant-select-arrow,
.ant-calendar-picker-icon {
  svg {
    color: #00aaa6;

    &:hover {
      color: #00b5b1;
    }
  }
}

/* 按钮样式覆盖 */
.ant-btn {
  border-radius: 4px;

  &.ant-btn-primary {
    background-color: #bae8e6; // 默认非选中状态的背景色
    border-color: #bae8e6;
    color: #333; // 非选中状态的文字颜色

    &.active {
      background-color: #00aaa6; // 选中状态的背景色
      border-color: #00aaa6;
      color: #fff; // 选中状态的文字颜色
    }

    &:hover,
    &:focus {
      background-color: #33c3bf;
      border-color: #33c3bf;
    }
  }
}

/* 图标样式覆盖 */
.ant-icon {
  color: #666;

  &:hover {
    color: #00b5b1;
  }
}

/* 暗色主题下的图标样式 */
.dark-theme .ant-icon {
  color: #ffffff;
}

.dark-theme .moreOperator_btn .ant-icon {
  color: #ffffff;
}

/* 表格容器样式 */
.ant-table-wrapper {
  padding: 0 16px;
}

/* 模态框头部样式 */
.ant-modal-header {
  padding: 0;
  border: none;
  border-radius: 0;
}

/* 模态框关闭按钮样式 */
.ant-modal-close-x {
  color: black;
}

/* 弹出框样式 */
.ant-popover {
  z-index: 1000;
}

.ant-popover-placement-bottom,
.ant-popover-placement-bottomRight,
.ant-popover-placement-bottomLeft {
  padding-right: 16px;
}

.ant-popover-inner-content {
  padding: 0;
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed;
  /* 改为固定定位，不随滚动条移动 */
  right: 20px;
  bottom: 40px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #00aaa6;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 99;
  transition: all 0.3s ease;
  font-size: 18px;

  /* 确保在内容区域有侧边栏时按钮位置正确 */
  .content-area-with-sidebar & {
    right: 420px;
    /* 当侧边栏打开时，调整按钮位置，确保它仍在内容区域内 */
  }
}

.back-to-top:hover {
  background-color: #008784;
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-theme .back-to-top {
  background-color: #00fbfb;
  color: #001d2c;
}

.dark-theme .back-to-top:hover {
  background-color: #00d8d8;
}

/* AI总结样式 */
.ai-summary-content {
  margin-bottom: 16px;
  padding: 16px;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.dark-content {
  background-color: #08343c !important;
  color: #ffffff !important;
}

.dark-text {
  color: #ffffff !important;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 24px;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 8px;
}

.dark-markdown h1 {
  border-bottom-color: #00aaa6 !important;
  color: #ffffff !important;
}

.markdown-content h2 {
  font-size: 20px;
}

.dark-markdown h2,
.dark-markdown h3,
.dark-markdown h4,
.dark-markdown h5,
.dark-markdown h6,
.dark-markdown p,
.dark-markdown li,
.dark-markdown strong,
.dark-markdown em {
  color: #ffffff !important;
}

.markdown-content h3 {
  font-size: 18px;
}

.markdown-content h4 {
  font-size: 16px;
}

.markdown-content p {
  margin-bottom: 12px;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 24px;
  margin-bottom: 12px;
}

.markdown-content li {
  margin-bottom: 4px;
}

.markdown-content code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}

.dark-markdown code {
  background-color: #001d2c !important;
  color: #00fbfb !important;
}

.markdown-content pre {
  background-color: #f0f0f0;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin-bottom: 12px;
}

.dark-markdown pre {
  background-color: #001d2c !important;
}

.markdown-content pre code {
  background-color: transparent;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #dfe2e5;
  padding-left: 16px;
  color: #6a737d;
  margin-bottom: 12px;
}

.dark-markdown blockquote {
  border-left-color: #00aaa6 !important;
  color: #a0a0a0 !important;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 12px;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #dfe2e5;
  padding: 8px;
}

.dark-markdown th,
.dark-markdown td {
  border-color: #00aaa6 !important;
  color: #ffffff !important;
}

.markdown-content th {
  background-color: #f6f8fa;
}

.dark-markdown th {
  background-color: #08343c !important;
}

.markdown-content tr:nth-child(even) {
  background-color: #f6f8fa;
}

.dark-markdown tr:nth-child(even) {
  background-color: #001d2c !important;
}

.markdown-content strong {
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
}

.ant-modal-wrap {
  z-index: 1000;
  /* 确保模态框在适当的层级 */
}

/* 滚动文本组件样式 */
.chart-scrolling-text {
  display: inline-block;
  margin-left: 16px;
  margin-right: 16px;
  max-width: calc(100% - 430px);
  vertical-align: middle;
  overflow: hidden;
}

/* 暗色主题下的滚动文本样式 */
.dark-theme .chart-scrolling-text {
  color: #fff;
}

/* 卡片标题样式调整 */
.card-title {
  padding-bottom: 8px;
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

/* 标题文本样式 */
.card-title-text {
  white-space: nowrap;
}

/* 按钮组样式调整 */
.quality-tab-btns,
.lost-tab-btns {
  margin-left: auto;
  white-space: nowrap;
}

/* 交付图表滚动文本样式 - 没有按钮，可以占用更多空间 */
.full-card .chart-scrolling-text {
  max-width: calc(100% - 550px);
}


</style>
