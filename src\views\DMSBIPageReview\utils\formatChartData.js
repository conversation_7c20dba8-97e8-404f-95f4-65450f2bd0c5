/**
 * 格式化日生产计划执行率数据为文本
 * @param {Object} data - 日生产计划执行率数据
 * @returns {String} - 格式化后的文本
 */
export const formatDeliveryData = (data) => {
  if (!data || !data.details || !Array.isArray(data.details) || data.details.length === 0) {
    return '';
  }

  // 基本信息
  const baseInfo = `当日计划产量: ${data.totalProductSchedule || 0}，当日实际产量: ${data.totalProductActual || 0}，当日计划达成率: ${data.productionProgress || 0}%`;

  // 按小时详情 - 直接使用后端返回的已排序数据，不进行排序
  const hourlyDetails = data.details
    .map(item => {
      // 修正达成率计算逻辑：当计划产量为0时，达成率应为0%
      let achievementRate = 0;
      if (item.planQty > 0) {
        achievementRate = ((item.productYeild / item.planQty) * 100).toFixed(2);
      }
      return `${item.productHour}时: 计划${item.planQty}台，实际${item.productYeild}台，达成率${achievementRate}%`;
    })
    .join('；');

  return `${baseInfo}。小时详情：${hourlyDetails}`;
};

/**
 * 格式化直通率数据为文本
 * @param {Object} data - 直通率数据
 * @returns {String} - 格式化后的文本
 */
export const formatFtyData = (data) => {
  if (!data) return '';

  // 基本信息
  const baseInfo = `当日不良数: ${data.totalDefectQty || 0}，当日产量: ${data.totalProductQty || 0}，当日良品率: ${data.totalPassThroughRate || 0}%`;

  // 不良原因详情
  let detailInfo = '';
  if (data.tables && data.tables.length > 0) {
    detailInfo = data.tables
      .map(item => `${item.causeName}: ${item.totalDefectQty}台`)
      .join('；');
    return `${baseInfo}。不良原因：${detailInfo}`;
  }

  return baseInfo;
};

/**
 * 格式化焊接泄漏率数据为文本
 * @param {Object} data - 焊接泄漏率数据
 * @returns {String} - 格式化后的文本
 */
export const formatWeldingLeakData = (data) => {
  if (!data) return '';

  // 基本信息
  const baseInfo = `当前泄露数: ${data.currentLeakCount || 0}，当前产量: ${data.currentProduction || 0}，当前泄露率: ${data.currentLeakRate || 0}%`;

  // 泄露原因详情
  let detailInfo = '';
  if (data.tables && data.tables.length > 0) {
    detailInfo = data.tables
      .map(item => `${item.causeName}: ${item.weldingLeakQty}台`)
      .join('；');
    return `${baseInfo}。泄露原因：${detailInfo}`;
  }

  return baseInfo;
};

/**
 * 格式化OQC抽检不良率数据为文本
 * @param {Object} data - OQC抽检不良率数据
 * @returns {String} - 格式化后的文本
 */
export const formatOqcData = (data) => {
  if (!data) return '';

  // 基本信息
  const baseInfo = `当前不良数: ${data.rejectQty || 0}，当前抽检总数: ${data.outputQty || 0}，抽检不良率: ${data.oqcRatio || '0%'}`;

  // 不良原因详情
  let detailInfo = '';
  if (data.detail && data.detail.length > 0) {
    detailInfo = data.detail
      .map(item => `${item.dpNm}: ${item.quantity}台`)
      .join('；');
    return `${baseInfo}。不良原因：${detailInfo}`;
  }

  return baseInfo;
};

/**
 * 格式化停产损失时间数据为文本
 * @param {Object} data - 停产损失时间数据
 * @returns {String} - 格式化后的文本
 */
export const formatDowntimeLossData = (data) => {
  if (!data) return '';

  // 基本信息
  const baseInfo = `当前停产时间: ${data.currentDowntimeDuration || 0}分钟，当前停产次数: ${data.currentDowntimeCount || 0}次`;

  // 点位详情
  let detailInfo = '';
  if (data.tables && data.tables.length > 0) {
    detailInfo = data.tables
      .map(item => `${item.pointDesc}: ${item.totalTime}分钟`)
      .join('；');
    return `${baseInfo}。点位详情：${detailInfo}`;
  }

  return baseInfo;
};

/**
 * 格式化过站损失时间数据为文本
 * @param {Object} data - 过站损失时间数据
 * @returns {String} - 格式化后的文本
 */
export const formatStationLossData = (data) => {
  if (!data) return '';

  // 基本信息
  const baseInfo = `总损失时间: ${data.totalLostTime || 0}分钟，总停线次数: ${data.totalLostNum || 0}次`;

  // 工位详情
  let detailInfo = '';
  if (data.tables && data.tables.length > 0) {
    detailInfo = data.tables
      .map(item => `${item.stationName}: ${item.totalLostTime}分钟，${item.totalLostNum}次`)
      .join('；');
    return `${baseInfo}。工位详情：${detailInfo}`;
  }

  return baseInfo;
};

export default {
  formatDeliveryData,
  formatFtyData,
  formatWeldingLeakData,
  formatOqcData,
  formatDowntimeLossData,
  formatStationLossData
};
