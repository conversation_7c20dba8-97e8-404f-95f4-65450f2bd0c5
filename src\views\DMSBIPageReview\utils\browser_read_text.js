// 全局变量用于存储当前播放的语音实例
let currentUtterance = null;
let isPaused = false;

// 导入marked库
import { marked } from "marked";

// 暂停播放
function pauseSpeech() {
  if (speechSynthesis && !isPaused) {
    speechSynthesis.pause();
    isPaused = true;
  }
}

// 继续播放
function resumeSpeech() {
  if (speechSynthesis && isPaused) {
    speechSynthesis.resume();
    isPaused = false;
  }
}

// 停止播放
function stopSpeech() {
  if (speechSynthesis) {
    speechSynthesis.cancel();
    currentUtterance = null;
    isPaused = false;
  }
}

// 获取当前播放状态
function getSpeechState() {
  return {
    isPlaying: !!currentUtterance && !isPaused,
    isPaused: isPaused
  };
}

/**
 * @description 文字转语音方法
 * @public
 * @param { text, rate, lang, volume, pitch } object
 * @param  text 要合成的文字内容，字符串
 * @param  rate 读取文字的语速 0.1~10  正常1
 * @param  lang 读取文字时的语言
 * @param  volume  读取时声音的音量 0~1  正常1
 * @param  pitch  读取时声音的音高 0~2  正常1
 * @returns SpeechSynthesisUtterance
 */
function speak(
  { text, speechRate, lang, volume, pitch },
  endEvent,
  startEvent
) {
  if (!window.SpeechSynthesisUtterance) {
    console.warn("当前浏览器不支持文字转语音服务");
    return;
  }
  if (!text) {
    return;
  }

  // 如果有正在播放的语音，先停止它
  if (currentUtterance) {
    speechSynthesis.cancel();
  }

  const speechUtterance = new SpeechSynthesisUtterance();
  speechUtterance.text = text;
  speechUtterance.rate = speechRate || 1;
  speechUtterance.lang = lang || "zh-CN";
  speechUtterance.volume = volume || 1;
  speechUtterance.pitch = pitch || 1;
  speechUtterance.onend = function() {
    currentUtterance = null;
    isPaused = false;
    endEvent && endEvent();
  };
  speechUtterance.onstart = function() {
    startEvent && startEvent();
  };

  currentUtterance = speechUtterance;
  isPaused = false;
  speechSynthesis.speak(speechUtterance);
  return speechUtterance;
}

/**
 * @description 复制文本
 * @public
 * @param { text } string
 * @returns Promise<boolean>
 */
function handleAiCopy(text) {
  return navigator.clipboard
    .writeText(text)
    .then(() => {
      return true;
    })
    .catch(() => {
      return false;
    });
}

/**
 * @description 渲染Markdown文本为HTML
 * @public
 * @param {string} markdownText 要渲染的Markdown文本
 * @param {Object} options marked选项配置
 * @returns {string} 渲染后的HTML字符串
 */
function renderMarkdown(markdownText, options = {}) {
  if (!markdownText) {
    return "";
  }

  // 设置默认选项
  const defaultOptions = {
    gfm: false, // GitHub风格Markdown
    breaks: true, // 允许回车换行
    sanitize: false, // 不进行HTML标签过滤
    smartLists: true, // 使用更智能的列表行为
    smartypants: false, // 不使用更漂亮的标点符号
    xhtml: false // 不使用自闭合标签
  };

  // 合并选项
  const markedOptions = { ...defaultOptions, ...options };

  // 设置marked选项
  marked.setOptions(markedOptions);

  // 渲染Markdown为HTML
  try {
    return marked(markdownText);
  } catch (error) {
    console.error("Markdown渲染错误:", error);
    return markdownText; // 如果出错，返回原始文本
  }
}

export {
  speak,
  handleAiCopy,
  pauseSpeech,
  resumeSpeech,
  stopSpeech,
  getSpeechState,
  renderMarkdown
};
