# ChartDataManager 图表数据管理器

## 概述

`ChartDataManager` 是一个统一的图表数据管理器，用于解决原有架构中API调用层级冗余和重复代码的问题。它将所有图表数据的获取、处理和更新逻辑统一管理，提供了简洁的API接口。

## 解决的问题

### 🔧 原有架构问题

1. **多层包装冗余**：API → echarts/dataFetchers.js → index.vue 方法，存在重复包装
2. **重复参数构建**：每个方法都重复构建相同的 `orgParams` 结构
3. **相似错误处理**：每个方法都有相同的 try-catch 逻辑
4. **重复更新逻辑**：更新图表、滚动文本、tooltip 的模式都很相似
5. **代码维护困难**：分散在多个文件中，修改需要同步多处

### ✅ 重构后的优势

1. **统一管理**：所有图表数据操作集中在一个类中
2. **减少冗余**：消除了中间层的重复包装
3. **配置驱动**：通过配置对象驱动不同图表的处理逻辑
4. **易于维护**：修改只需要在一个地方进行
5. **类型安全**：统一的参数格式和错误处理

## 架构设计

### 新架构流程

```
Vue组件 → ChartDataManager → API直接调用
```

### 配置驱动模式

每个图表类型都有对应的配置对象：

```javascript
{
  delivery: {
    apiMethod: getDailyProductPlanRate,      // API方法
    updateMethod: updateDeliveryChartData,   // 图表更新方法
    formatMethod: formatDeliveryData,        // 数据格式化方法
    chartRef: 'deliveryChart',               // 图表引用
    dataPath: 'chartTexts.delivery',         // 滚动文本路径
    hasDataFlag: 'hasDeliveryData',          // 数据有效标志
    overviewPath: 'deliver',                 // 概览数据路径
    overviewMapping: { ... },                // 概览数据映射
    tooltipReset: 'resetDeliveryTooltip',    // tooltip重置方法
    fallbackChart: 'initDeliveryChart'       // 默认图表方法
  }
}
```

## 核心功能

### 1. 统一参数构建

```javascript
buildQueryParams(searchForm) {
  return {
    date: searchForm.date || moment().format('YYYY-MM-DD'),
    orgParams: {
      companyCode: searchForm.baseName,
      companyName: searchForm.baseCode,
      // ... 其他参数
    }
  };
}
```

### 2. 统一数据获取和更新

```javascript
async fetchAndUpdateChart(chartType, searchForm) {
  // 1. 构建参数
  // 2. 调用API
  // 3. 更新概览数据
  // 4. 更新图表
  // 5. 更新滚动文本
  // 6. 重置tooltip
}
```

### 3. 批量更新

```javascript
async updateAllCharts(searchForm) {
  // 并行获取所有图表数据
  const promises = Object.keys(this.chartConfigs).map(async (chartType) => {
    return await this.fetchAndUpdateChart(chartType, searchForm);
  });
  
  await Promise.all(promises);
}
```

### 4. 按钮驱动更新

```javascript
async updateChartByButton(buttonType, searchForm) {
  const typeMapping = {
    'FTY': 'fty',
    'OQC': 'oqc', 
    'WELDING': 'welding',
    'stop_production': 'downtimeLoss',
    'fault': 'stationLoss'
  };
  
  const chartType = typeMapping[buttonType];
  return await this.fetchAndUpdateChart(chartType, searchForm);
}
```

## 使用方法

### 1. 初始化

```javascript
// 在Vue组件的mounted钩子中
this.chartDataManager = createChartDataManager(this);
```

### 2. 更新所有图表

```javascript
// 当搜索条件变化时
if (this.chartDataManager) {
  this.chartDataManager.updateAllCharts(this.searchForm);
}
```

### 3. 更新特定图表

```javascript
// 当按钮切换时
if (this.chartDataManager) {
  this.chartDataManager.updateChartByButton(buttonType, this.searchForm);
}
```

## 支持的图表类型

| 图表类型 | 按钮类型 | API方法 | 说明 |
|---------|---------|---------|------|
| delivery | - | getDailyProductPlanRate | 日生产计划执行率 |
| fty | FTY | getPassThroughRate | 直通率 |
| oqc | OQC | queryOqcSpotCheck | OQC抽检不良率 |
| welding | WELDING | getWeldingLeak | 焊接泄漏率 |
| downtimeLoss | stop_production | getDowntimeLoss | 停产损失时间 |
| stationLoss | fault | getStationLoss | 过站损失时间 |

## 错误处理

### 统一错误处理策略

1. **API调用失败**：自动回退到默认图表
2. **数据格式错误**：使用空数据渲染
3. **网络异常**：显示错误状态并提供重试机制
4. **参数验证**：在调用前验证必要参数

### 错误日志

所有错误都会记录详细的日志信息：

```javascript
console.error(`获取${chartType}数据失败:`, error);
```

## 性能优化

### 1. 并行请求

所有图表数据并行获取，减少总体加载时间：

```javascript
const promises = Object.keys(this.chartConfigs).map(async (chartType) => {
  return await this.fetchAndUpdateChart(chartType, searchForm);
});

await Promise.all(promises);
```

### 2. 智能更新

只在必要时更新图表，避免不必要的重渲染：

```javascript
if (this.context.isInitializingOrg) {
  console.log(`组织层级初始化中，跳过${chartType}图表数据获取`);
  return false;
}
```

### 3. 内存管理

自动清理定时器和事件监听器，避免内存泄漏。

## 扩展性

### 添加新图表类型

只需要在 `chartConfigs` 中添加新的配置对象：

```javascript
newChart: {
  apiMethod: getNewChartData,
  updateMethod: updateNewChartData,
  formatMethod: formatNewChartData,
  chartRef: 'newChart',
  dataPath: 'chartTexts.newChart',
  hasDataFlag: 'hasNewChartData',
  // ... 其他配置
}
```

### 自定义处理逻辑

可以通过继承 `ChartDataManager` 类来添加自定义逻辑：

```javascript
class CustomChartDataManager extends ChartDataManager {
  async fetchAndUpdateChart(chartType, searchForm) {
    // 自定义逻辑
    const result = await super.fetchAndUpdateChart(chartType, searchForm);
    // 后处理逻辑
    return result;
  }
}
```

## 迁移指南

### 从旧架构迁移

1. **移除重复方法**：删除 index.vue 中的 `fetchXXXData` 方法
2. **更新调用方式**：使用 `chartDataManager.updateAllCharts()` 替代原有方法
3. **简化监听器**：使用 `chartDataManager.updateChartByButton()` 处理按钮切换
4. **保留兼容性**：保留原有方法名，内部调用新的管理器

### 注意事项

1. **初始化顺序**：确保在组织层级初始化完成后再调用图表更新
2. **错误处理**：检查 `chartDataManager` 是否已初始化
3. **生命周期**：在组件销毁时清理管理器资源

## 总结

`ChartDataManager` 通过统一的配置驱动模式，大大简化了图表数据管理的复杂性，提高了代码的可维护性和扩展性。它是一个典型的重构案例，展示了如何通过合理的架构设计来解决代码冗余和维护困难的问题。
